'use client'

import { ProtectedRoute } from '@/components/ProtectedRoute'
import { Header } from '@/components/Header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Car, Plus, Battery, MapPin, Settings } from 'lucide-react'

function VehiclesContent() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header variant="dashboard" />

      {/* Main Content */}
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Vehicles</h1>
            <p className="text-gray-600 dark:text-gray-300">
              Manage and monitor your electric vehicles
            </p>
          </div>
          <Button className="bg-electric-600 hover:bg-electric-700">
            <Plus className="mr-2 h-4 w-4" />
            Add Vehicle
          </Button>
        </div>

        {/* Vehicles Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Vehicle 1 */}
          <Card className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Car className="h-5 w-5 text-electric-600" />
                  <CardTitle className="text-lg">Tesla Model 3</CardTitle>
                </div>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Active
                </Badge>
              </div>
              <CardDescription>2023 • Long Range • White</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Battery Status */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center">
                    <Battery className="mr-1 h-4 w-4 text-electric-600" />
                    Battery
                  </span>
                  <span className="font-medium">85%</span>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div className="h-2 rounded-full bg-electric-600" style={{ width: '85%' }}></div>
                </div>
                <p className="text-xs text-gray-500">247 miles remaining</p>
              </div>

              {/* Location */}
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="mr-2 h-4 w-4" />
                <span>Home Garage</span>
              </div>

              {/* Actions */}
              <div className="flex space-x-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Settings className="mr-1 h-3 w-3" />
                  Settings
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle 2 */}
          <Card className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Car className="h-5 w-5 text-electric-600" />
                  <CardTitle className="text-lg">Nissan Leaf</CardTitle>
                </div>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  Charging
                </Badge>
              </div>
              <CardDescription>2022 • SV Plus • Blue</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Battery Status */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center">
                    <Battery className="mr-1 h-4 w-4 text-electric-600" />
                    Battery
                  </span>
                  <span className="font-medium">62%</span>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div className="h-2 rounded-full bg-electric-600" style={{ width: '62%' }}></div>
                </div>
                <p className="text-xs text-gray-500">Charging • 2h 15m remaining</p>
              </div>

              {/* Location */}
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="mr-2 h-4 w-4" />
                <span>Tesla Supercharger - Downtown</span>
              </div>

              {/* Actions */}
              <div className="flex space-x-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Settings className="mr-1 h-3 w-3" />
                  Settings
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Add Vehicle Card */}
          <Card className="border-2 border-dashed border-gray-300 bg-gray-50 transition-colors hover:border-electric-300 hover:bg-electric-50 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-electric-400 dark:hover:bg-electric-900/20">
            <CardContent className="flex h-full flex-col items-center justify-center p-6 text-center">
              <Plus className="mb-4 h-12 w-12 text-gray-400 dark:text-gray-500" />
              <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
                Add New Vehicle
              </h3>
              <p className="mb-4 text-sm text-gray-600 dark:text-gray-300">
                Connect your electric vehicle to start tracking
              </p>
              <Button
                variant="outline"
                className="border-electric-600 text-electric-600 hover:bg-electric-600 hover:text-white dark:border-electric-400 dark:text-electric-400 dark:hover:bg-electric-600 dark:hover:text-white"
              >
                Get Started
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Quick Stats */}
        <div className="mt-8 grid gap-6 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Car className="h-8 w-8 text-electric-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Vehicles</p>
                  <p className="text-2xl font-bold text-gray-900">2</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Battery className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Battery</p>
                  <p className="text-2xl font-bold text-gray-900">73%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MapPin className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Range</p>
                  <p className="text-2xl font-bold text-gray-900">389 mi</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Settings className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-gray-900">1</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}

export default function VehiclesPage() {
  return (
    <ProtectedRoute>
      <VehiclesContent />
    </ProtectedRoute>
  )
}
