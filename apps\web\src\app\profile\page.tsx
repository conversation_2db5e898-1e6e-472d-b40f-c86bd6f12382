'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/ProtectedRoute'
import { Header } from '@/components/Header'
import { AvatarUpload } from '@/components/AvatarUpload'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Save,
  Edit3,
  Shield,
  Bell,
  CreditCard,
  Download,
} from 'lucide-react'

function ProfileContent() {
  const { user, profile } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    fullName: profile?.full_name || '',
    email: user?.email || '',
    phone: profile?.phone || '',
    address: profile?.address || '',
    dateOfBirth: profile?.date_of_birth || '',
    bio: profile?.bio || '',
  })
  const [avatarFile, setAvatarFile] = useState<File | null>(null)

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    // TODO: Implement profile update logic
    console.log('Saving profile:', formData)
    if (avatarFile) {
      console.log('Uploading avatar:', avatarFile)
    }
    setIsEditing(false)
  }

  const handleAvatarChange = (file: File | null) => {
    setAvatarFile(file)
  }

  const handleCancel = () => {
    setFormData({
      fullName: profile?.full_name || '',
      email: user?.email || '',
      phone: profile?.phone || '',
      address: profile?.address || '',
      dateOfBirth: profile?.date_of_birth || '',
      bio: profile?.bio || '',
    })
    setIsEditing(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header variant="dashboard" />

      {/* Main Content */}
      <main className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage your personal information and account preferences
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Profile Overview Card */}
          <div className="lg:col-span-1">
            <Card className="dark:border-gray-700 dark:bg-gray-800">
              <CardHeader className="text-center">
                <div className="mb-4">
                  <AvatarUpload
                    currentAvatar={profile?.avatar_url}
                    onAvatarChange={handleAvatarChange}
                    size="md"
                    editable={isEditing}
                  />
                </div>
                <CardTitle className="dark:text-white">
                  {formData.fullName || 'User Name'}
                </CardTitle>
                <CardDescription className="dark:text-gray-300">{formData.email}</CardDescription>
                <Badge
                  variant="secondary"
                  className="mt-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                >
                  Verified Account
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <Calendar className="mr-2 h-4 w-4" />
                  <span>Joined December 2024</span>
                </div>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <MapPin className="mr-2 h-4 w-4" />
                  <span>{formData.address || 'No address provided'}</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="mt-6 dark:border-gray-700 dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="text-lg dark:text-white">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                  asChild
                >
                  <Link href="/settings">
                    <Shield className="mr-2 h-4 w-4" />
                    Security Settings
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                >
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                >
                  <CreditCard className="mr-2 h-4 w-4" />
                  Billing & Plans
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start dark:border-gray-600 dark:text-gray-300"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export Data
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Profile Details Form */}
          <div className="lg:col-span-2">
            <Card className="dark:border-gray-700 dark:bg-gray-800">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="dark:text-white">Personal Information</CardTitle>
                  <CardDescription className="dark:text-gray-300">
                    Update your personal details and contact information
                  </CardDescription>
                </div>
                {!isEditing ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="dark:border-gray-600 dark:text-gray-300"
                  >
                    <Edit3 className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancel}
                      className="dark:border-gray-600 dark:text-gray-300"
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      className="bg-electric-600 hover:bg-electric-700"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save
                    </Button>
                  </div>
                )}
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="fullName" className="dark:text-gray-300">
                      Full Name
                    </Label>
                    <Input
                      id="fullName"
                      value={formData.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      disabled={!isEditing}
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="dark:text-gray-300">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      disabled={!isEditing}
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="dark:text-gray-300">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      disabled={!isEditing}
                      placeholder="+****************"
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth" className="dark:text-gray-300">
                      Date of Birth
                    </Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                      disabled={!isEditing}
                      className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address" className="dark:text-gray-300">
                    Address
                  </Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    disabled={!isEditing}
                    placeholder="123 Main St, City, State 12345"
                    className="dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio" className="dark:text-gray-300">
                    Bio
                  </Label>
                  <textarea
                    id="bio"
                    value={formData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Tell us a bit about yourself..."
                    rows={4}
                    className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-900 placeholder-gray-500 focus:border-electric-500 focus:outline-none focus:ring-1 focus:ring-electric-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Account Statistics */}
            <Card className="mt-6 dark:border-gray-700 dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="dark:text-white">Account Statistics</CardTitle>
                <CardDescription className="dark:text-gray-300">
                  Your activity and usage overview
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-electric-600">2</div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Vehicles</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">47</div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Charging Sessions</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">1,247</div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Miles Driven</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <ProfileContent />
    </ProtectedRoute>
  )
}
