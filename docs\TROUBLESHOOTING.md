# Troubleshooting Guide

Common issues and solutions for the GreenMilesEV project.

## 🚀 Getting Started Issues

### Environment Setup

#### Issue: "Missing Supabase environment variables"
```
Error: Missing Supabase environment variables
```

**Solution:**
1. Copy environment template files:
   ```bash
   cp apps/web/.env.local.example apps/web/.env.local
   cp apps/mobile/.env.example apps/mobile/.env
   ```
2. Get your Supabase credentials from the [dashboard](https://supabase.com/dashboard/project/pbevpexclffmhqstwlha/settings/api)
3. Fill in the actual values in your environment files

#### Issue: Environment variables not loading in mobile app
**Solution:**
- Make sure you're using `EXPO_PUBLIC_` prefix for mobile environment variables
- Restart the Expo development server after changing environment variables
- Clear Expo cache: `expo start --clear`

### Installation Issues

#### Issue: npm install fails with dependency conflicts
**Solution:**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json
rm -rf apps/*/node_modules apps/*/package-lock.json

# Reinstall
npm install
```

#### Issue: Expo CLI not found
**Solution:**
```bash
# Install Expo CLI globally
npm install -g @expo/cli

# Or use npx
npx expo start
```

## 🗄️ Database Issues

### Connection Problems

#### Issue: "Invalid API key" or authentication errors
**Solution:**
1. Verify your Supabase project is active
2. Check that API keys are correct and not expired
3. Ensure you're using the anon key for client-side operations
4. Verify the project URL matches your actual project

#### Issue: RLS policy violations
```
Error: new row violates row-level security policy
```

**Solution:**
1. Make sure user is authenticated before database operations
2. Check that RLS policies allow the operation
3. Verify `user_id` matches `auth.uid()` in your data
4. Test policies in Supabase SQL editor:
   ```sql
   SELECT auth.uid(); -- Should return your user ID
   SELECT * FROM profiles WHERE id = auth.uid();
   ```

### Data Issues

#### Issue: Profile not created automatically on signup
**Solution:**
1. Check if the trigger function exists:
   ```sql
   SELECT * FROM pg_proc WHERE proname = 'handle_new_user';
   ```
2. Verify the trigger is active:
   ```sql
   SELECT * FROM pg_trigger WHERE tgname = 'on_auth_user_created';
   ```
3. Manually create profile if needed:
   ```sql
   INSERT INTO profiles (id, email) 
   VALUES (auth.uid(), '<EMAIL>');
   ```

#### Issue: Charging stations not appearing
**Solution:**
1. Check if sample data exists:
   ```sql
   SELECT COUNT(*) FROM charging_stations;
   ```
2. Verify RLS policy allows reading:
   ```sql
   SELECT * FROM charging_stations LIMIT 5;
   ```
3. Check network connectivity and API calls

## 🌐 Web App Issues

### Next.js Problems

#### Issue: "Module not found" errors for shared package
**Solution:**
1. Check TypeScript path configuration in `tsconfig.json`:
   ```json
   {
     "paths": {
       "@/shared/*": ["../../packages/shared/src/*"]
     }
   }
   ```
2. Restart the development server
3. Clear Next.js cache: `rm -rf .next`

#### Issue: Tailwind styles not applying
**Solution:**
1. Check `tailwind.config.js` content paths include all source files
2. Verify `globals.css` imports Tailwind directives
3. Restart development server
4. Check for CSS conflicts or specificity issues

#### Issue: Supabase client errors in browser
**Solution:**
1. Check browser console for detailed error messages
2. Verify environment variables are loaded (check Network tab)
3. Ensure CORS is properly configured in Supabase
4. Check if you're using client-side vs server-side operations correctly

### Build Issues

#### Issue: Build fails with TypeScript errors
**Solution:**
1. Run type checking: `npm run type-check`
2. Fix any TypeScript errors
3. Ensure all imports are correct
4. Check for missing dependencies

## 📱 Mobile App Issues

### Expo Problems

#### Issue: "Expo CLI is not installed"
**Solution:**
```bash
# Install latest Expo CLI
npm install -g @expo/cli

# Or use the new local CLI
npx create-expo-app --template
```

#### Issue: Metro bundler fails to start
**Solution:**
```bash
# Clear Metro cache
npx expo start --clear

# Or manually clear cache
rm -rf node_modules/.cache
rm -rf .expo
```

#### Issue: "Unable to resolve module" errors
**Solution:**
1. Check if the module is installed: `npm list <module-name>`
2. Clear cache and restart: `expo start --clear`
3. Check import paths are correct
4. Ensure TypeScript paths are configured correctly

### React Native Issues

#### Issue: NativeWind styles not working
**Solution:**
1. Check `babel.config.js` includes NativeWind plugin:
   ```javascript
   plugins: ['nativewind/babel']
   ```
2. Verify `tailwind.config.js` content paths
3. Import global CSS in `_layout.tsx`:
   ```typescript
   import '../global.css'
   ```
4. Restart Expo development server

#### Issue: Navigation not working
**Solution:**
1. Check Expo Router file structure matches expected patterns
2. Verify all screen files are in correct directories
3. Check for TypeScript errors in navigation files
4. Ensure `expo-router` is properly configured in `app.json`

### Device Testing

#### Issue: App crashes on physical device
**Solution:**
1. Check Expo Go app is up to date
2. Ensure device and computer are on same network
3. Check for console errors in Expo DevTools
4. Try creating a development build for better debugging

## 🔐 Authentication Issues

### Sign Up Problems

#### Issue: Email confirmation not working
**Solution:**
1. Check email confirmation is enabled in Supabase Auth settings
2. Verify email templates are configured
3. Check spam folder for confirmation emails
4. Test with a different email provider

#### Issue: OAuth providers not working
**Solution:**
1. Configure OAuth providers in Supabase dashboard
2. Add redirect URLs for your domains
3. Verify client IDs and secrets are correct
4. Check OAuth provider settings (Google, GitHub, etc.)

### Session Management

#### Issue: User session not persisting
**Solution:**
1. Check if `persistSession` is enabled in Supabase client config
2. Verify storage is working (AsyncStorage for mobile, localStorage for web)
3. Check for session expiration issues
4. Ensure auth state is properly managed in your app

## 🔧 Development Issues

### Hot Reload Problems

#### Issue: Changes not reflecting in browser/app
**Solution:**
1. Check if development server is running
2. Hard refresh browser (Ctrl+Shift+R)
3. Clear cache and restart development server
4. Check for syntax errors preventing compilation

### TypeScript Issues

#### Issue: Type errors in shared package imports
**Solution:**
1. Build the shared package: `cd packages/shared && npm run build`
2. Check TypeScript configuration in consuming apps
3. Verify export statements in shared package
4. Restart TypeScript language server in your IDE

### Performance Issues

#### Issue: Slow database queries
**Solution:**
1. Check query performance in Supabase dashboard
2. Add appropriate indexes for your queries
3. Use `select()` to limit returned columns
4. Implement pagination for large datasets
5. Consider using database functions for complex operations

#### Issue: Large bundle size
**Solution:**
1. Analyze bundle with Next.js analyzer or Expo bundle analyzer
2. Use dynamic imports for large components
3. Remove unused dependencies
4. Optimize images and assets

## 🆘 Getting Help

### Debug Information to Collect

When reporting issues, include:

1. **Environment Information:**
   ```bash
   node --version
   npm --version
   expo --version  # for mobile
   ```

2. **Error Messages:**
   - Full error stack trace
   - Browser console errors
   - Expo DevTools errors

3. **Configuration:**
   - Relevant config file contents
   - Environment variable setup (without sensitive values)
   - Package.json dependencies

4. **Steps to Reproduce:**
   - Exact steps that cause the issue
   - Expected vs actual behavior
   - Screenshots or screen recordings if helpful

### Useful Commands for Debugging

```bash
# Check all package versions
npm list

# Verify environment variables (web)
cd apps/web && npm run dev
# Check browser console for process.env values

# Verify environment variables (mobile)
cd apps/mobile && expo start
# Check Expo DevTools for environment

# Test database connection
# Use Supabase SQL editor to run test queries

# Clear all caches
npm run clean
cd apps/web && rm -rf .next
cd apps/mobile && expo start --clear
```

### Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Community Support

- [Supabase Discord](https://discord.supabase.com/)
- [Expo Discord](https://chat.expo.dev/)
- [Next.js Discussions](https://github.com/vercel/next.js/discussions)
- [React Native Community](https://reactnative.dev/community/overview)
