"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tailwind-merge";
exports.ids = ["vendor-chunks/tailwind-merge"];
exports.modules = {

/***/ "(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = \"-\";\nconst createClassGroupUtils = (config)=>{\n    const classMap = createClassMap(config);\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config;\n    const getClassGroupId = (className)=>{\n        const classParts = className.split(CLASS_PART_SEPARATOR);\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === \"\" && classParts.length !== 1) {\n            classParts.shift();\n        }\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n    };\n    const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier)=>{\n        const conflicts = conflictingClassGroups[classGroupId] || [];\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [\n                ...conflicts,\n                ...conflictingClassGroupModifiers[classGroupId]\n            ];\n        }\n        return conflicts;\n    };\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds\n    };\n};\nconst getGroupRecursive = (classParts, classPartObject)=>{\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId;\n    }\n    const currentClassPart = classParts[0];\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n    const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart;\n    }\n    if (classPartObject.validators.length === 0) {\n        return undefined;\n    }\n    const classRest = classParts.join(CLASS_PART_SEPARATOR);\n    return classPartObject.validators.find(({ validator })=>validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = (className)=>{\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n        const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(\":\"));\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return \"arbitrary..\" + property;\n        }\n    }\n};\n/**\n * Exported for testing only\n */ const createClassMap = (config)=>{\n    const { theme, prefix } = config;\n    const classMap = {\n        nextPart: new Map(),\n        validators: []\n    };\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup])=>{\n        processClassesRecursively(classGroup, classMap, classGroupId, theme);\n    });\n    return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme)=>{\n    classGroup.forEach((classDefinition)=>{\n        if (typeof classDefinition === \"string\") {\n            const classPartObjectToEdit = classDefinition === \"\" ? classPartObject : getPart(classPartObject, classDefinition);\n            classPartObjectToEdit.classGroupId = classGroupId;\n            return;\n        }\n        if (typeof classDefinition === \"function\") {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n                return;\n            }\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId\n            });\n            return;\n        }\n        Object.entries(classDefinition).forEach(([key, classGroup])=>{\n            processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n        });\n    });\n};\nconst getPart = (classPartObject, path)=>{\n    let currentClassPartObject = classPartObject;\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart)=>{\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: []\n            });\n        }\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n    });\n    return currentClassPartObject;\n};\nconst isThemeGetter = (func)=>func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix)=>{\n    if (!prefix) {\n        return classGroupEntries;\n    }\n    return classGroupEntries.map(([classGroupId, classGroup])=>{\n        const prefixedClassGroup = classGroup.map((classDefinition)=>{\n            if (typeof classDefinition === \"string\") {\n                return prefix + classDefinition;\n            }\n            if (typeof classDefinition === \"object\") {\n                return Object.fromEntries(Object.entries(classDefinition).map(([key, value])=>[\n                        prefix + key,\n                        value\n                    ]));\n            }\n            return classDefinition;\n        });\n        return [\n            classGroupId,\n            prefixedClassGroup\n        ];\n    });\n};\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = (maxCacheSize)=>{\n    if (maxCacheSize < 1) {\n        return {\n            get: ()=>undefined,\n            set: ()=>{}\n        };\n    }\n    let cacheSize = 0;\n    let cache = new Map();\n    let previousCache = new Map();\n    const update = (key, value)=>{\n        cache.set(key, value);\n        cacheSize++;\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0;\n            previousCache = cache;\n            cache = new Map();\n        }\n    };\n    return {\n        get (key) {\n            let value = cache.get(key);\n            if (value !== undefined) {\n                return value;\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value);\n                return value;\n            }\n        },\n        set (key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value);\n            } else {\n                update(key, value);\n            }\n        }\n    };\n};\nconst IMPORTANT_MODIFIER = \"!\";\nconst createParseClassName = (config)=>{\n    const { separator, experimentalParseClassName } = config;\n    const isSeparatorSingleCharacter = separator.length === 1;\n    const firstSeparatorCharacter = separator[0];\n    const separatorLength = separator.length;\n    // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    const parseClassName = (className)=>{\n        const modifiers = [];\n        let bracketDepth = 0;\n        let modifierStart = 0;\n        let postfixModifierPosition;\n        for(let index = 0; index < className.length; index++){\n            let currentCharacter = className[index];\n            if (bracketDepth === 0) {\n                if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n                    modifiers.push(className.slice(modifierStart, index));\n                    modifierStart = index + separatorLength;\n                    continue;\n                }\n                if (currentCharacter === \"/\") {\n                    postfixModifierPosition = index;\n                    continue;\n                }\n            }\n            if (currentCharacter === \"[\") {\n                bracketDepth++;\n            } else if (currentCharacter === \"]\") {\n                bracketDepth--;\n            }\n        }\n        const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n        const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n        const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n        const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition\n        };\n    };\n    if (experimentalParseClassName) {\n        return (className)=>experimentalParseClassName({\n                className,\n                parseClassName\n            });\n    }\n    return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */ const sortModifiers = (modifiers)=>{\n    if (modifiers.length <= 1) {\n        return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach((modifier)=>{\n        const isArbitraryVariant = modifier[0] === \"[\";\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n            unsortedModifiers = [];\n        } else {\n            unsortedModifiers.push(modifier);\n        }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n};\nconst createConfigUtils = (config)=>({\n        cache: createLruCache(config.cacheSize),\n        parseClassName: createParseClassName(config),\n        ...createClassGroupUtils(config)\n    });\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils)=>{\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds } = configUtils;\n    /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */ const classGroupsInConflict = [];\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n    let result = \"\";\n    for(let index = classNames.length - 1; index >= 0; index -= 1){\n        const originalClassName = classNames[index];\n        const { modifiers, hasImportantModifier, baseClassName, maybePostfixModifierPosition } = parseClassName(originalClassName);\n        let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n        let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? \" \" + result : result);\n                continue;\n            }\n            classGroupId = getClassGroupId(baseClassName);\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? \" \" + result : result);\n                continue;\n            }\n            hasPostfixModifier = false;\n        }\n        const variantModifier = sortModifiers(modifiers).join(\":\");\n        const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n        const classId = modifierId + classGroupId;\n        if (classGroupsInConflict.includes(classId)) {\n            continue;\n        }\n        classGroupsInConflict.push(classId);\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n        for(let i = 0; i < conflictGroups.length; ++i){\n            const group = conflictGroups[i];\n            classGroupsInConflict.push(modifierId + group);\n        }\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? \" \" + result : result);\n    }\n    return result;\n};\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */ function twJoin() {\n    let index = 0;\n    let argument;\n    let resolvedValue;\n    let string = \"\";\n    while(index < arguments.length){\n        if (argument = arguments[index++]) {\n            if (resolvedValue = toValue(argument)) {\n                string && (string += \" \");\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n}\nconst toValue = (mix)=>{\n    if (typeof mix === \"string\") {\n        return mix;\n    }\n    let resolvedValue;\n    let string = \"\";\n    for(let k = 0; k < mix.length; k++){\n        if (mix[k]) {\n            if (resolvedValue = toValue(mix[k])) {\n                string && (string += \" \");\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n    let configUtils;\n    let cacheGet;\n    let cacheSet;\n    let functionToCall = initTailwindMerge;\n    function initTailwindMerge(classList) {\n        const config = createConfigRest.reduce((previousConfig, createConfigCurrent)=>createConfigCurrent(previousConfig), createConfigFirst());\n        configUtils = createConfigUtils(config);\n        cacheGet = configUtils.cache.get;\n        cacheSet = configUtils.cache.set;\n        functionToCall = tailwindMerge;\n        return tailwindMerge(classList);\n    }\n    function tailwindMerge(classList) {\n        const cachedResult = cacheGet(classList);\n        if (cachedResult) {\n            return cachedResult;\n        }\n        const result = mergeClassList(classList, configUtils);\n        cacheSet(classList, result);\n        return result;\n    }\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments));\n    };\n}\nconst fromTheme = (key)=>{\n    const themeGetter = (theme)=>theme[key] || [];\n    themeGetter.isThemeGetter = true;\n    return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/ new Set([\n    \"px\",\n    \"full\",\n    \"screen\"\n]);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = (value)=>isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = (value)=>getIsArbitraryValue(value, \"length\", isLengthOnly);\nconst isNumber = (value)=>Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = (value)=>getIsArbitraryValue(value, \"number\", isNumber);\nconst isInteger = (value)=>Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = (value)=>value.endsWith(\"%\") && isNumber(value.slice(0, -1));\nconst isArbitraryValue = (value)=>arbitraryValueRegex.test(value);\nconst isTshirtSize = (value)=>tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/ new Set([\n    \"length\",\n    \"size\",\n    \"percentage\"\n]);\nconst isArbitrarySize = (value)=>getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = (value)=>getIsArbitraryValue(value, \"position\", isNever);\nconst imageLabels = /*#__PURE__*/ new Set([\n    \"image\",\n    \"url\"\n]);\nconst isArbitraryImage = (value)=>getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = (value)=>getIsArbitraryValue(value, \"\", isShadow);\nconst isAny = ()=>true;\nconst getIsArbitraryValue = (value, label, testValue)=>{\n    const result = arbitraryValueRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return typeof label === \"string\" ? result[1] === label : label.has(result[1]);\n        }\n        return testValue(result[2]);\n    }\n    return false;\n};\nconst isLengthOnly = (value)=>// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = ()=>false;\nconst isShadow = (value)=>shadowRegex.test(value);\nconst isImage = (value)=>imageRegex.test(value);\nconst validators = /*#__PURE__*/ Object.defineProperty({\n    __proto__: null,\n    isAny,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isInteger,\n    isLength,\n    isNumber,\n    isPercent,\n    isTshirtSize\n}, Symbol.toStringTag, {\n    value: \"Module\"\n});\nconst getDefaultConfig = ()=>{\n    const colors = fromTheme(\"colors\");\n    const spacing = fromTheme(\"spacing\");\n    const blur = fromTheme(\"blur\");\n    const brightness = fromTheme(\"brightness\");\n    const borderColor = fromTheme(\"borderColor\");\n    const borderRadius = fromTheme(\"borderRadius\");\n    const borderSpacing = fromTheme(\"borderSpacing\");\n    const borderWidth = fromTheme(\"borderWidth\");\n    const contrast = fromTheme(\"contrast\");\n    const grayscale = fromTheme(\"grayscale\");\n    const hueRotate = fromTheme(\"hueRotate\");\n    const invert = fromTheme(\"invert\");\n    const gap = fromTheme(\"gap\");\n    const gradientColorStops = fromTheme(\"gradientColorStops\");\n    const gradientColorStopPositions = fromTheme(\"gradientColorStopPositions\");\n    const inset = fromTheme(\"inset\");\n    const margin = fromTheme(\"margin\");\n    const opacity = fromTheme(\"opacity\");\n    const padding = fromTheme(\"padding\");\n    const saturate = fromTheme(\"saturate\");\n    const scale = fromTheme(\"scale\");\n    const sepia = fromTheme(\"sepia\");\n    const skew = fromTheme(\"skew\");\n    const space = fromTheme(\"space\");\n    const translate = fromTheme(\"translate\");\n    const getOverscroll = ()=>[\n            \"auto\",\n            \"contain\",\n            \"none\"\n        ];\n    const getOverflow = ()=>[\n            \"auto\",\n            \"hidden\",\n            \"clip\",\n            \"visible\",\n            \"scroll\"\n        ];\n    const getSpacingWithAutoAndArbitrary = ()=>[\n            \"auto\",\n            isArbitraryValue,\n            spacing\n        ];\n    const getSpacingWithArbitrary = ()=>[\n            isArbitraryValue,\n            spacing\n        ];\n    const getLengthWithEmptyAndArbitrary = ()=>[\n            \"\",\n            isLength,\n            isArbitraryLength\n        ];\n    const getNumberWithAutoAndArbitrary = ()=>[\n            \"auto\",\n            isNumber,\n            isArbitraryValue\n        ];\n    const getPositions = ()=>[\n            \"bottom\",\n            \"center\",\n            \"left\",\n            \"left-bottom\",\n            \"left-top\",\n            \"right\",\n            \"right-bottom\",\n            \"right-top\",\n            \"top\"\n        ];\n    const getLineStyles = ()=>[\n            \"solid\",\n            \"dashed\",\n            \"dotted\",\n            \"double\",\n            \"none\"\n        ];\n    const getBlendModes = ()=>[\n            \"normal\",\n            \"multiply\",\n            \"screen\",\n            \"overlay\",\n            \"darken\",\n            \"lighten\",\n            \"color-dodge\",\n            \"color-burn\",\n            \"hard-light\",\n            \"soft-light\",\n            \"difference\",\n            \"exclusion\",\n            \"hue\",\n            \"saturation\",\n            \"color\",\n            \"luminosity\"\n        ];\n    const getAlign = ()=>[\n            \"start\",\n            \"end\",\n            \"center\",\n            \"between\",\n            \"around\",\n            \"evenly\",\n            \"stretch\"\n        ];\n    const getZeroAndEmpty = ()=>[\n            \"\",\n            \"0\",\n            isArbitraryValue\n        ];\n    const getBreaks = ()=>[\n            \"auto\",\n            \"avoid\",\n            \"all\",\n            \"avoid-page\",\n            \"page\",\n            \"left\",\n            \"right\",\n            \"column\"\n        ];\n    const getNumberAndArbitrary = ()=>[\n            isNumber,\n            isArbitraryValue\n        ];\n    return {\n        cacheSize: 500,\n        separator: \":\",\n        theme: {\n            colors: [\n                isAny\n            ],\n            spacing: [\n                isLength,\n                isArbitraryLength\n            ],\n            blur: [\n                \"none\",\n                \"\",\n                isTshirtSize,\n                isArbitraryValue\n            ],\n            brightness: getNumberAndArbitrary(),\n            borderColor: [\n                colors\n            ],\n            borderRadius: [\n                \"none\",\n                \"\",\n                \"full\",\n                isTshirtSize,\n                isArbitraryValue\n            ],\n            borderSpacing: getSpacingWithArbitrary(),\n            borderWidth: getLengthWithEmptyAndArbitrary(),\n            contrast: getNumberAndArbitrary(),\n            grayscale: getZeroAndEmpty(),\n            hueRotate: getNumberAndArbitrary(),\n            invert: getZeroAndEmpty(),\n            gap: getSpacingWithArbitrary(),\n            gradientColorStops: [\n                colors\n            ],\n            gradientColorStopPositions: [\n                isPercent,\n                isArbitraryLength\n            ],\n            inset: getSpacingWithAutoAndArbitrary(),\n            margin: getSpacingWithAutoAndArbitrary(),\n            opacity: getNumberAndArbitrary(),\n            padding: getSpacingWithArbitrary(),\n            saturate: getNumberAndArbitrary(),\n            scale: getNumberAndArbitrary(),\n            sepia: getZeroAndEmpty(),\n            skew: getNumberAndArbitrary(),\n            space: getSpacingWithArbitrary(),\n            translate: getSpacingWithArbitrary()\n        },\n        classGroups: {\n            // Layout\n            /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */ aspect: [\n                {\n                    aspect: [\n                        \"auto\",\n                        \"square\",\n                        \"video\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */ container: [\n                \"container\"\n            ],\n            /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */ columns: [\n                {\n                    columns: [\n                        isTshirtSize\n                    ]\n                }\n            ],\n            /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */ \"break-after\": [\n                {\n                    \"break-after\": getBreaks()\n                }\n            ],\n            /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */ \"break-before\": [\n                {\n                    \"break-before\": getBreaks()\n                }\n            ],\n            /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */ \"break-inside\": [\n                {\n                    \"break-inside\": [\n                        \"auto\",\n                        \"avoid\",\n                        \"avoid-page\",\n                        \"avoid-column\"\n                    ]\n                }\n            ],\n            /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */ \"box-decoration\": [\n                {\n                    \"box-decoration\": [\n                        \"slice\",\n                        \"clone\"\n                    ]\n                }\n            ],\n            /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */ box: [\n                {\n                    box: [\n                        \"border\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */ display: [\n                \"block\",\n                \"inline-block\",\n                \"inline\",\n                \"flex\",\n                \"inline-flex\",\n                \"table\",\n                \"inline-table\",\n                \"table-caption\",\n                \"table-cell\",\n                \"table-column\",\n                \"table-column-group\",\n                \"table-footer-group\",\n                \"table-header-group\",\n                \"table-row-group\",\n                \"table-row\",\n                \"flow-root\",\n                \"grid\",\n                \"inline-grid\",\n                \"contents\",\n                \"list-item\",\n                \"hidden\"\n            ],\n            /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */ float: [\n                {\n                    float: [\n                        \"right\",\n                        \"left\",\n                        \"none\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */ clear: [\n                {\n                    clear: [\n                        \"left\",\n                        \"right\",\n                        \"both\",\n                        \"none\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */ isolation: [\n                \"isolate\",\n                \"isolation-auto\"\n            ],\n            /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */ \"object-fit\": [\n                {\n                    object: [\n                        \"contain\",\n                        \"cover\",\n                        \"fill\",\n                        \"none\",\n                        \"scale-down\"\n                    ]\n                }\n            ],\n            /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */ \"object-position\": [\n                {\n                    object: [\n                        ...getPositions(),\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */ overflow: [\n                {\n                    overflow: getOverflow()\n                }\n            ],\n            /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */ \"overflow-x\": [\n                {\n                    \"overflow-x\": getOverflow()\n                }\n            ],\n            /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */ \"overflow-y\": [\n                {\n                    \"overflow-y\": getOverflow()\n                }\n            ],\n            /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ overscroll: [\n                {\n                    overscroll: getOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ \"overscroll-x\": [\n                {\n                    \"overscroll-x\": getOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ \"overscroll-y\": [\n                {\n                    \"overscroll-y\": getOverscroll()\n                }\n            ],\n            /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */ position: [\n                \"static\",\n                \"fixed\",\n                \"absolute\",\n                \"relative\",\n                \"sticky\"\n            ],\n            /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ inset: [\n                {\n                    inset: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ \"inset-x\": [\n                {\n                    \"inset-x\": [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ \"inset-y\": [\n                {\n                    \"inset-y\": [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ start: [\n                {\n                    start: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ end: [\n                {\n                    end: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ top: [\n                {\n                    top: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ right: [\n                {\n                    right: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ bottom: [\n                {\n                    bottom: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ left: [\n                {\n                    left: [\n                        inset\n                    ]\n                }\n            ],\n            /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */ visibility: [\n                \"visible\",\n                \"invisible\",\n                \"collapse\"\n            ],\n            /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */ z: [\n                {\n                    z: [\n                        \"auto\",\n                        isInteger,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Flexbox and Grid\n            /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */ basis: [\n                {\n                    basis: getSpacingWithAutoAndArbitrary()\n                }\n            ],\n            /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */ \"flex-direction\": [\n                {\n                    flex: [\n                        \"row\",\n                        \"row-reverse\",\n                        \"col\",\n                        \"col-reverse\"\n                    ]\n                }\n            ],\n            /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */ \"flex-wrap\": [\n                {\n                    flex: [\n                        \"wrap\",\n                        \"wrap-reverse\",\n                        \"nowrap\"\n                    ]\n                }\n            ],\n            /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */ flex: [\n                {\n                    flex: [\n                        \"1\",\n                        \"auto\",\n                        \"initial\",\n                        \"none\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */ grow: [\n                {\n                    grow: getZeroAndEmpty()\n                }\n            ],\n            /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */ shrink: [\n                {\n                    shrink: getZeroAndEmpty()\n                }\n            ],\n            /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */ order: [\n                {\n                    order: [\n                        \"first\",\n                        \"last\",\n                        \"none\",\n                        isInteger,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */ \"grid-cols\": [\n                {\n                    \"grid-cols\": [\n                        isAny\n                    ]\n                }\n            ],\n            /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ \"col-start-end\": [\n                {\n                    col: [\n                        \"auto\",\n                        {\n                            span: [\n                                \"full\",\n                                isInteger,\n                                isArbitraryValue\n                            ]\n                        },\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */ \"col-start\": [\n                {\n                    \"col-start\": getNumberWithAutoAndArbitrary()\n                }\n            ],\n            /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ \"col-end\": [\n                {\n                    \"col-end\": getNumberWithAutoAndArbitrary()\n                }\n            ],\n            /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */ \"grid-rows\": [\n                {\n                    \"grid-rows\": [\n                        isAny\n                    ]\n                }\n            ],\n            /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ \"row-start-end\": [\n                {\n                    row: [\n                        \"auto\",\n                        {\n                            span: [\n                                isInteger,\n                                isArbitraryValue\n                            ]\n                        },\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */ \"row-start\": [\n                {\n                    \"row-start\": getNumberWithAutoAndArbitrary()\n                }\n            ],\n            /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ \"row-end\": [\n                {\n                    \"row-end\": getNumberWithAutoAndArbitrary()\n                }\n            ],\n            /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */ \"grid-flow\": [\n                {\n                    \"grid-flow\": [\n                        \"row\",\n                        \"col\",\n                        \"dense\",\n                        \"row-dense\",\n                        \"col-dense\"\n                    ]\n                }\n            ],\n            /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */ \"auto-cols\": [\n                {\n                    \"auto-cols\": [\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fr\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */ \"auto-rows\": [\n                {\n                    \"auto-rows\": [\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fr\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */ gap: [\n                {\n                    gap: [\n                        gap\n                    ]\n                }\n            ],\n            /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */ \"gap-x\": [\n                {\n                    \"gap-x\": [\n                        gap\n                    ]\n                }\n            ],\n            /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */ \"gap-y\": [\n                {\n                    \"gap-y\": [\n                        gap\n                    ]\n                }\n            ],\n            /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */ \"justify-content\": [\n                {\n                    justify: [\n                        \"normal\",\n                        ...getAlign()\n                    ]\n                }\n            ],\n            /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */ \"justify-items\": [\n                {\n                    \"justify-items\": [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */ \"justify-self\": [\n                {\n                    \"justify-self\": [\n                        \"auto\",\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */ \"align-content\": [\n                {\n                    content: [\n                        \"normal\",\n                        ...getAlign(),\n                        \"baseline\"\n                    ]\n                }\n            ],\n            /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */ \"align-items\": [\n                {\n                    items: [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"baseline\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */ \"align-self\": [\n                {\n                    self: [\n                        \"auto\",\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\",\n                        \"baseline\"\n                    ]\n                }\n            ],\n            /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */ \"place-content\": [\n                {\n                    \"place-content\": [\n                        ...getAlign(),\n                        \"baseline\"\n                    ]\n                }\n            ],\n            /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */ \"place-items\": [\n                {\n                    \"place-items\": [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"baseline\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */ \"place-self\": [\n                {\n                    \"place-self\": [\n                        \"auto\",\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            // Spacing\n            /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */ p: [\n                {\n                    p: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */ px: [\n                {\n                    px: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */ py: [\n                {\n                    py: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */ ps: [\n                {\n                    ps: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */ pe: [\n                {\n                    pe: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */ pt: [\n                {\n                    pt: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */ pr: [\n                {\n                    pr: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */ pb: [\n                {\n                    pb: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */ pl: [\n                {\n                    pl: [\n                        padding\n                    ]\n                }\n            ],\n            /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */ m: [\n                {\n                    m: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */ mx: [\n                {\n                    mx: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */ my: [\n                {\n                    my: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */ ms: [\n                {\n                    ms: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */ me: [\n                {\n                    me: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */ mt: [\n                {\n                    mt: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */ mr: [\n                {\n                    mr: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */ mb: [\n                {\n                    mb: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */ ml: [\n                {\n                    ml: [\n                        margin\n                    ]\n                }\n            ],\n            /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */ \"space-x\": [\n                {\n                    \"space-x\": [\n                        space\n                    ]\n                }\n            ],\n            /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */ \"space-x-reverse\": [\n                \"space-x-reverse\"\n            ],\n            /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */ \"space-y\": [\n                {\n                    \"space-y\": [\n                        space\n                    ]\n                }\n            ],\n            /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */ \"space-y-reverse\": [\n                \"space-y-reverse\"\n            ],\n            // Sizing\n            /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */ w: [\n                {\n                    w: [\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        \"svw\",\n                        \"lvw\",\n                        \"dvw\",\n                        isArbitraryValue,\n                        spacing\n                    ]\n                }\n            ],\n            /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */ \"min-w\": [\n                {\n                    \"min-w\": [\n                        isArbitraryValue,\n                        spacing,\n                        \"min\",\n                        \"max\",\n                        \"fit\"\n                    ]\n                }\n            ],\n            /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */ \"max-w\": [\n                {\n                    \"max-w\": [\n                        isArbitraryValue,\n                        spacing,\n                        \"none\",\n                        \"full\",\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        \"prose\",\n                        {\n                            screen: [\n                                isTshirtSize\n                            ]\n                        },\n                        isTshirtSize\n                    ]\n                }\n            ],\n            /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */ h: [\n                {\n                    h: [\n                        isArbitraryValue,\n                        spacing,\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        \"svh\",\n                        \"lvh\",\n                        \"dvh\"\n                    ]\n                }\n            ],\n            /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */ \"min-h\": [\n                {\n                    \"min-h\": [\n                        isArbitraryValue,\n                        spacing,\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        \"svh\",\n                        \"lvh\",\n                        \"dvh\"\n                    ]\n                }\n            ],\n            /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */ \"max-h\": [\n                {\n                    \"max-h\": [\n                        isArbitraryValue,\n                        spacing,\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        \"svh\",\n                        \"lvh\",\n                        \"dvh\"\n                    ]\n                }\n            ],\n            /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */ size: [\n                {\n                    size: [\n                        isArbitraryValue,\n                        spacing,\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fit\"\n                    ]\n                }\n            ],\n            // Typography\n            /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */ \"font-size\": [\n                {\n                    text: [\n                        \"base\",\n                        isTshirtSize,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */ \"font-smoothing\": [\n                \"antialiased\",\n                \"subpixel-antialiased\"\n            ],\n            /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */ \"font-style\": [\n                \"italic\",\n                \"not-italic\"\n            ],\n            /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */ \"font-weight\": [\n                {\n                    font: [\n                        \"thin\",\n                        \"extralight\",\n                        \"light\",\n                        \"normal\",\n                        \"medium\",\n                        \"semibold\",\n                        \"bold\",\n                        \"extrabold\",\n                        \"black\",\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */ \"font-family\": [\n                {\n                    font: [\n                        isAny\n                    ]\n                }\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-normal\": [\n                \"normal-nums\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-ordinal\": [\n                \"ordinal\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-slashed-zero\": [\n                \"slashed-zero\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-figure\": [\n                \"lining-nums\",\n                \"oldstyle-nums\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-spacing\": [\n                \"proportional-nums\",\n                \"tabular-nums\"\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ \"fvn-fraction\": [\n                \"diagonal-fractions\",\n                \"stacked-fractions\"\n            ],\n            /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */ tracking: [\n                {\n                    tracking: [\n                        \"tighter\",\n                        \"tight\",\n                        \"normal\",\n                        \"wide\",\n                        \"wider\",\n                        \"widest\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */ \"line-clamp\": [\n                {\n                    \"line-clamp\": [\n                        \"none\",\n                        isNumber,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */ leading: [\n                {\n                    leading: [\n                        \"none\",\n                        \"tight\",\n                        \"snug\",\n                        \"normal\",\n                        \"relaxed\",\n                        \"loose\",\n                        isLength,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */ \"list-image\": [\n                {\n                    \"list-image\": [\n                        \"none\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */ \"list-style-type\": [\n                {\n                    list: [\n                        \"none\",\n                        \"disc\",\n                        \"decimal\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */ \"list-style-position\": [\n                {\n                    list: [\n                        \"inside\",\n                        \"outside\"\n                    ]\n                }\n            ],\n            /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */ \"placeholder-color\": [\n                {\n                    placeholder: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */ \"placeholder-opacity\": [\n                {\n                    \"placeholder-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */ \"text-alignment\": [\n                {\n                    text: [\n                        \"left\",\n                        \"center\",\n                        \"right\",\n                        \"justify\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */ \"text-color\": [\n                {\n                    text: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */ \"text-opacity\": [\n                {\n                    \"text-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */ \"text-decoration\": [\n                \"underline\",\n                \"overline\",\n                \"line-through\",\n                \"no-underline\"\n            ],\n            /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */ \"text-decoration-style\": [\n                {\n                    decoration: [\n                        ...getLineStyles(),\n                        \"wavy\"\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */ \"text-decoration-thickness\": [\n                {\n                    decoration: [\n                        \"auto\",\n                        \"from-font\",\n                        isLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */ \"underline-offset\": [\n                {\n                    \"underline-offset\": [\n                        \"auto\",\n                        isLength,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */ \"text-decoration-color\": [\n                {\n                    decoration: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */ \"text-transform\": [\n                \"uppercase\",\n                \"lowercase\",\n                \"capitalize\",\n                \"normal-case\"\n            ],\n            /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */ \"text-overflow\": [\n                \"truncate\",\n                \"text-ellipsis\",\n                \"text-clip\"\n            ],\n            /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */ \"text-wrap\": [\n                {\n                    text: [\n                        \"wrap\",\n                        \"nowrap\",\n                        \"balance\",\n                        \"pretty\"\n                    ]\n                }\n            ],\n            /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */ indent: [\n                {\n                    indent: getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */ \"vertical-align\": [\n                {\n                    align: [\n                        \"baseline\",\n                        \"top\",\n                        \"middle\",\n                        \"bottom\",\n                        \"text-top\",\n                        \"text-bottom\",\n                        \"sub\",\n                        \"super\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */ whitespace: [\n                {\n                    whitespace: [\n                        \"normal\",\n                        \"nowrap\",\n                        \"pre\",\n                        \"pre-line\",\n                        \"pre-wrap\",\n                        \"break-spaces\"\n                    ]\n                }\n            ],\n            /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */ break: [\n                {\n                    break: [\n                        \"normal\",\n                        \"words\",\n                        \"all\",\n                        \"keep\"\n                    ]\n                }\n            ],\n            /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */ hyphens: [\n                {\n                    hyphens: [\n                        \"none\",\n                        \"manual\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */ content: [\n                {\n                    content: [\n                        \"none\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Backgrounds\n            /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */ \"bg-attachment\": [\n                {\n                    bg: [\n                        \"fixed\",\n                        \"local\",\n                        \"scroll\"\n                    ]\n                }\n            ],\n            /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */ \"bg-clip\": [\n                {\n                    \"bg-clip\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\",\n                        \"text\"\n                    ]\n                }\n            ],\n            /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */ \"bg-opacity\": [\n                {\n                    \"bg-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */ \"bg-origin\": [\n                {\n                    \"bg-origin\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */ \"bg-position\": [\n                {\n                    bg: [\n                        ...getPositions(),\n                        isArbitraryPosition\n                    ]\n                }\n            ],\n            /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */ \"bg-repeat\": [\n                {\n                    bg: [\n                        \"no-repeat\",\n                        {\n                            repeat: [\n                                \"\",\n                                \"x\",\n                                \"y\",\n                                \"round\",\n                                \"space\"\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */ \"bg-size\": [\n                {\n                    bg: [\n                        \"auto\",\n                        \"cover\",\n                        \"contain\",\n                        isArbitrarySize\n                    ]\n                }\n            ],\n            /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */ \"bg-image\": [\n                {\n                    bg: [\n                        \"none\",\n                        {\n                            \"gradient-to\": [\n                                \"t\",\n                                \"tr\",\n                                \"r\",\n                                \"br\",\n                                \"b\",\n                                \"bl\",\n                                \"l\",\n                                \"tl\"\n                            ]\n                        },\n                        isArbitraryImage\n                    ]\n                }\n            ],\n            /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */ \"bg-color\": [\n                {\n                    bg: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-from-pos\": [\n                {\n                    from: [\n                        gradientColorStopPositions\n                    ]\n                }\n            ],\n            /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-via-pos\": [\n                {\n                    via: [\n                        gradientColorStopPositions\n                    ]\n                }\n            ],\n            /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-to-pos\": [\n                {\n                    to: [\n                        gradientColorStopPositions\n                    ]\n                }\n            ],\n            /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-from\": [\n                {\n                    from: [\n                        gradientColorStops\n                    ]\n                }\n            ],\n            /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-via\": [\n                {\n                    via: [\n                        gradientColorStops\n                    ]\n                }\n            ],\n            /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ \"gradient-to\": [\n                {\n                    to: [\n                        gradientColorStops\n                    ]\n                }\n            ],\n            // Borders\n            /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */ rounded: [\n                {\n                    rounded: [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-s\": [\n                {\n                    \"rounded-s\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-e\": [\n                {\n                    \"rounded-e\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-t\": [\n                {\n                    \"rounded-t\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-r\": [\n                {\n                    \"rounded-r\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-b\": [\n                {\n                    \"rounded-b\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-l\": [\n                {\n                    \"rounded-l\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-ss\": [\n                {\n                    \"rounded-ss\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-se\": [\n                {\n                    \"rounded-se\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-ee\": [\n                {\n                    \"rounded-ee\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-es\": [\n                {\n                    \"rounded-es\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-tl\": [\n                {\n                    \"rounded-tl\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-tr\": [\n                {\n                    \"rounded-tr\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-br\": [\n                {\n                    \"rounded-br\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ \"rounded-bl\": [\n                {\n                    \"rounded-bl\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w\": [\n                {\n                    border: [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-x\": [\n                {\n                    \"border-x\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-y\": [\n                {\n                    \"border-y\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-s\": [\n                {\n                    \"border-s\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-e\": [\n                {\n                    \"border-e\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-t\": [\n                {\n                    \"border-t\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-r\": [\n                {\n                    \"border-r\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-b\": [\n                {\n                    \"border-b\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */ \"border-w-l\": [\n                {\n                    \"border-l\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */ \"border-opacity\": [\n                {\n                    \"border-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */ \"border-style\": [\n                {\n                    border: [\n                        ...getLineStyles(),\n                        \"hidden\"\n                    ]\n                }\n            ],\n            /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */ \"divide-x\": [\n                {\n                    \"divide-x\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */ \"divide-x-reverse\": [\n                \"divide-x-reverse\"\n            ],\n            /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */ \"divide-y\": [\n                {\n                    \"divide-y\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */ \"divide-y-reverse\": [\n                \"divide-y-reverse\"\n            ],\n            /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */ \"divide-opacity\": [\n                {\n                    \"divide-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */ \"divide-style\": [\n                {\n                    divide: getLineStyles()\n                }\n            ],\n            /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color\": [\n                {\n                    border: [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-x\": [\n                {\n                    \"border-x\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-y\": [\n                {\n                    \"border-y\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-s\": [\n                {\n                    \"border-s\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-e\": [\n                {\n                    \"border-e\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-t\": [\n                {\n                    \"border-t\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-r\": [\n                {\n                    \"border-r\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-b\": [\n                {\n                    \"border-b\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */ \"border-color-l\": [\n                {\n                    \"border-l\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */ \"divide-color\": [\n                {\n                    divide: [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */ \"outline-style\": [\n                {\n                    outline: [\n                        \"\",\n                        ...getLineStyles()\n                    ]\n                }\n            ],\n            /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */ \"outline-offset\": [\n                {\n                    \"outline-offset\": [\n                        isLength,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */ \"outline-w\": [\n                {\n                    outline: [\n                        isLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */ \"outline-color\": [\n                {\n                    outline: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */ \"ring-w\": [\n                {\n                    ring: getLengthWithEmptyAndArbitrary()\n                }\n            ],\n            /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */ \"ring-w-inset\": [\n                \"ring-inset\"\n            ],\n            /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */ \"ring-color\": [\n                {\n                    ring: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */ \"ring-opacity\": [\n                {\n                    \"ring-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */ \"ring-offset-w\": [\n                {\n                    \"ring-offset\": [\n                        isLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */ \"ring-offset-color\": [\n                {\n                    \"ring-offset\": [\n                        colors\n                    ]\n                }\n            ],\n            // Effects\n            /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */ shadow: [\n                {\n                    shadow: [\n                        \"\",\n                        \"inner\",\n                        \"none\",\n                        isTshirtSize,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */ \"shadow-color\": [\n                {\n                    shadow: [\n                        isAny\n                    ]\n                }\n            ],\n            /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */ opacity: [\n                {\n                    opacity: [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */ \"mix-blend\": [\n                {\n                    \"mix-blend\": [\n                        ...getBlendModes(),\n                        \"plus-lighter\",\n                        \"plus-darker\"\n                    ]\n                }\n            ],\n            /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */ \"bg-blend\": [\n                {\n                    \"bg-blend\": getBlendModes()\n                }\n            ],\n            // Filters\n            /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */ filter: [\n                {\n                    filter: [\n                        \"\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */ blur: [\n                {\n                    blur: [\n                        blur\n                    ]\n                }\n            ],\n            /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */ brightness: [\n                {\n                    brightness: [\n                        brightness\n                    ]\n                }\n            ],\n            /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */ contrast: [\n                {\n                    contrast: [\n                        contrast\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */ \"drop-shadow\": [\n                {\n                    \"drop-shadow\": [\n                        \"\",\n                        \"none\",\n                        isTshirtSize,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */ grayscale: [\n                {\n                    grayscale: [\n                        grayscale\n                    ]\n                }\n            ],\n            /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */ \"hue-rotate\": [\n                {\n                    \"hue-rotate\": [\n                        hueRotate\n                    ]\n                }\n            ],\n            /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */ invert: [\n                {\n                    invert: [\n                        invert\n                    ]\n                }\n            ],\n            /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */ saturate: [\n                {\n                    saturate: [\n                        saturate\n                    ]\n                }\n            ],\n            /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */ sepia: [\n                {\n                    sepia: [\n                        sepia\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */ \"backdrop-filter\": [\n                {\n                    \"backdrop-filter\": [\n                        \"\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */ \"backdrop-blur\": [\n                {\n                    \"backdrop-blur\": [\n                        blur\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */ \"backdrop-brightness\": [\n                {\n                    \"backdrop-brightness\": [\n                        brightness\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */ \"backdrop-contrast\": [\n                {\n                    \"backdrop-contrast\": [\n                        contrast\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */ \"backdrop-grayscale\": [\n                {\n                    \"backdrop-grayscale\": [\n                        grayscale\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */ \"backdrop-hue-rotate\": [\n                {\n                    \"backdrop-hue-rotate\": [\n                        hueRotate\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */ \"backdrop-invert\": [\n                {\n                    \"backdrop-invert\": [\n                        invert\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */ \"backdrop-opacity\": [\n                {\n                    \"backdrop-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */ \"backdrop-saturate\": [\n                {\n                    \"backdrop-saturate\": [\n                        saturate\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */ \"backdrop-sepia\": [\n                {\n                    \"backdrop-sepia\": [\n                        sepia\n                    ]\n                }\n            ],\n            // Tables\n            /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */ \"border-collapse\": [\n                {\n                    border: [\n                        \"collapse\",\n                        \"separate\"\n                    ]\n                }\n            ],\n            /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ \"border-spacing\": [\n                {\n                    \"border-spacing\": [\n                        borderSpacing\n                    ]\n                }\n            ],\n            /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ \"border-spacing-x\": [\n                {\n                    \"border-spacing-x\": [\n                        borderSpacing\n                    ]\n                }\n            ],\n            /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ \"border-spacing-y\": [\n                {\n                    \"border-spacing-y\": [\n                        borderSpacing\n                    ]\n                }\n            ],\n            /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */ \"table-layout\": [\n                {\n                    table: [\n                        \"auto\",\n                        \"fixed\"\n                    ]\n                }\n            ],\n            /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */ caption: [\n                {\n                    caption: [\n                        \"top\",\n                        \"bottom\"\n                    ]\n                }\n            ],\n            // Transitions and Animation\n            /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */ transition: [\n                {\n                    transition: [\n                        \"none\",\n                        \"all\",\n                        \"\",\n                        \"colors\",\n                        \"opacity\",\n                        \"shadow\",\n                        \"transform\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */ duration: [\n                {\n                    duration: getNumberAndArbitrary()\n                }\n            ],\n            /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */ ease: [\n                {\n                    ease: [\n                        \"linear\",\n                        \"in\",\n                        \"out\",\n                        \"in-out\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */ delay: [\n                {\n                    delay: getNumberAndArbitrary()\n                }\n            ],\n            /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */ animate: [\n                {\n                    animate: [\n                        \"none\",\n                        \"spin\",\n                        \"ping\",\n                        \"pulse\",\n                        \"bounce\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Transforms\n            /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */ transform: [\n                {\n                    transform: [\n                        \"\",\n                        \"gpu\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */ scale: [\n                {\n                    scale: [\n                        scale\n                    ]\n                }\n            ],\n            /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */ \"scale-x\": [\n                {\n                    \"scale-x\": [\n                        scale\n                    ]\n                }\n            ],\n            /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */ \"scale-y\": [\n                {\n                    \"scale-y\": [\n                        scale\n                    ]\n                }\n            ],\n            /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */ rotate: [\n                {\n                    rotate: [\n                        isInteger,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */ \"translate-x\": [\n                {\n                    \"translate-x\": [\n                        translate\n                    ]\n                }\n            ],\n            /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */ \"translate-y\": [\n                {\n                    \"translate-y\": [\n                        translate\n                    ]\n                }\n            ],\n            /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */ \"skew-x\": [\n                {\n                    \"skew-x\": [\n                        skew\n                    ]\n                }\n            ],\n            /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */ \"skew-y\": [\n                {\n                    \"skew-y\": [\n                        skew\n                    ]\n                }\n            ],\n            /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */ \"transform-origin\": [\n                {\n                    origin: [\n                        \"center\",\n                        \"top\",\n                        \"top-right\",\n                        \"right\",\n                        \"bottom-right\",\n                        \"bottom\",\n                        \"bottom-left\",\n                        \"left\",\n                        \"top-left\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Interactivity\n            /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */ accent: [\n                {\n                    accent: [\n                        \"auto\",\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */ appearance: [\n                {\n                    appearance: [\n                        \"none\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */ cursor: [\n                {\n                    cursor: [\n                        \"auto\",\n                        \"default\",\n                        \"pointer\",\n                        \"wait\",\n                        \"text\",\n                        \"move\",\n                        \"help\",\n                        \"not-allowed\",\n                        \"none\",\n                        \"context-menu\",\n                        \"progress\",\n                        \"cell\",\n                        \"crosshair\",\n                        \"vertical-text\",\n                        \"alias\",\n                        \"copy\",\n                        \"no-drop\",\n                        \"grab\",\n                        \"grabbing\",\n                        \"all-scroll\",\n                        \"col-resize\",\n                        \"row-resize\",\n                        \"n-resize\",\n                        \"e-resize\",\n                        \"s-resize\",\n                        \"w-resize\",\n                        \"ne-resize\",\n                        \"nw-resize\",\n                        \"se-resize\",\n                        \"sw-resize\",\n                        \"ew-resize\",\n                        \"ns-resize\",\n                        \"nesw-resize\",\n                        \"nwse-resize\",\n                        \"zoom-in\",\n                        \"zoom-out\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */ \"caret-color\": [\n                {\n                    caret: [\n                        colors\n                    ]\n                }\n            ],\n            /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */ \"pointer-events\": [\n                {\n                    \"pointer-events\": [\n                        \"none\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */ resize: [\n                {\n                    resize: [\n                        \"none\",\n                        \"y\",\n                        \"x\",\n                        \"\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */ \"scroll-behavior\": [\n                {\n                    scroll: [\n                        \"auto\",\n                        \"smooth\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-m\": [\n                {\n                    \"scroll-m\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mx\": [\n                {\n                    \"scroll-mx\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-my\": [\n                {\n                    \"scroll-my\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-ms\": [\n                {\n                    \"scroll-ms\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-me\": [\n                {\n                    \"scroll-me\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mt\": [\n                {\n                    \"scroll-mt\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mr\": [\n                {\n                    \"scroll-mr\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-mb\": [\n                {\n                    \"scroll-mb\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ \"scroll-ml\": [\n                {\n                    \"scroll-ml\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-p\": [\n                {\n                    \"scroll-p\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-px\": [\n                {\n                    \"scroll-px\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-py\": [\n                {\n                    \"scroll-py\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-ps\": [\n                {\n                    \"scroll-ps\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pe\": [\n                {\n                    \"scroll-pe\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pt\": [\n                {\n                    \"scroll-pt\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pr\": [\n                {\n                    \"scroll-pr\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pb\": [\n                {\n                    \"scroll-pb\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ \"scroll-pl\": [\n                {\n                    \"scroll-pl\": getSpacingWithArbitrary()\n                }\n            ],\n            /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */ \"snap-align\": [\n                {\n                    snap: [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"align-none\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */ \"snap-stop\": [\n                {\n                    snap: [\n                        \"normal\",\n                        \"always\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ \"snap-type\": [\n                {\n                    snap: [\n                        \"none\",\n                        \"x\",\n                        \"y\",\n                        \"both\"\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ \"snap-strictness\": [\n                {\n                    snap: [\n                        \"mandatory\",\n                        \"proximity\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */ touch: [\n                {\n                    touch: [\n                        \"auto\",\n                        \"none\",\n                        \"manipulation\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */ \"touch-x\": [\n                {\n                    \"touch-pan\": [\n                        \"x\",\n                        \"left\",\n                        \"right\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */ \"touch-y\": [\n                {\n                    \"touch-pan\": [\n                        \"y\",\n                        \"up\",\n                        \"down\"\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */ \"touch-pz\": [\n                \"touch-pinch-zoom\"\n            ],\n            /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */ select: [\n                {\n                    select: [\n                        \"none\",\n                        \"text\",\n                        \"all\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */ \"will-change\": [\n                {\n                    \"will-change\": [\n                        \"auto\",\n                        \"scroll\",\n                        \"contents\",\n                        \"transform\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // SVG\n            /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */ fill: [\n                {\n                    fill: [\n                        colors,\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */ \"stroke-w\": [\n                {\n                    stroke: [\n                        isLength,\n                        isArbitraryLength,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */ stroke: [\n                {\n                    stroke: [\n                        colors,\n                        \"none\"\n                    ]\n                }\n            ],\n            // Accessibility\n            /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */ sr: [\n                \"sr-only\",\n                \"not-sr-only\"\n            ],\n            /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */ \"forced-color-adjust\": [\n                {\n                    \"forced-color-adjust\": [\n                        \"auto\",\n                        \"none\"\n                    ]\n                }\n            ]\n        },\n        conflictingClassGroups: {\n            overflow: [\n                \"overflow-x\",\n                \"overflow-y\"\n            ],\n            overscroll: [\n                \"overscroll-x\",\n                \"overscroll-y\"\n            ],\n            inset: [\n                \"inset-x\",\n                \"inset-y\",\n                \"start\",\n                \"end\",\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ],\n            \"inset-x\": [\n                \"right\",\n                \"left\"\n            ],\n            \"inset-y\": [\n                \"top\",\n                \"bottom\"\n            ],\n            flex: [\n                \"basis\",\n                \"grow\",\n                \"shrink\"\n            ],\n            gap: [\n                \"gap-x\",\n                \"gap-y\"\n            ],\n            p: [\n                \"px\",\n                \"py\",\n                \"ps\",\n                \"pe\",\n                \"pt\",\n                \"pr\",\n                \"pb\",\n                \"pl\"\n            ],\n            px: [\n                \"pr\",\n                \"pl\"\n            ],\n            py: [\n                \"pt\",\n                \"pb\"\n            ],\n            m: [\n                \"mx\",\n                \"my\",\n                \"ms\",\n                \"me\",\n                \"mt\",\n                \"mr\",\n                \"mb\",\n                \"ml\"\n            ],\n            mx: [\n                \"mr\",\n                \"ml\"\n            ],\n            my: [\n                \"mt\",\n                \"mb\"\n            ],\n            size: [\n                \"w\",\n                \"h\"\n            ],\n            \"font-size\": [\n                \"leading\"\n            ],\n            \"fvn-normal\": [\n                \"fvn-ordinal\",\n                \"fvn-slashed-zero\",\n                \"fvn-figure\",\n                \"fvn-spacing\",\n                \"fvn-fraction\"\n            ],\n            \"fvn-ordinal\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-slashed-zero\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-figure\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-spacing\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-fraction\": [\n                \"fvn-normal\"\n            ],\n            \"line-clamp\": [\n                \"display\",\n                \"overflow\"\n            ],\n            rounded: [\n                \"rounded-s\",\n                \"rounded-e\",\n                \"rounded-t\",\n                \"rounded-r\",\n                \"rounded-b\",\n                \"rounded-l\",\n                \"rounded-ss\",\n                \"rounded-se\",\n                \"rounded-ee\",\n                \"rounded-es\",\n                \"rounded-tl\",\n                \"rounded-tr\",\n                \"rounded-br\",\n                \"rounded-bl\"\n            ],\n            \"rounded-s\": [\n                \"rounded-ss\",\n                \"rounded-es\"\n            ],\n            \"rounded-e\": [\n                \"rounded-se\",\n                \"rounded-ee\"\n            ],\n            \"rounded-t\": [\n                \"rounded-tl\",\n                \"rounded-tr\"\n            ],\n            \"rounded-r\": [\n                \"rounded-tr\",\n                \"rounded-br\"\n            ],\n            \"rounded-b\": [\n                \"rounded-br\",\n                \"rounded-bl\"\n            ],\n            \"rounded-l\": [\n                \"rounded-tl\",\n                \"rounded-bl\"\n            ],\n            \"border-spacing\": [\n                \"border-spacing-x\",\n                \"border-spacing-y\"\n            ],\n            \"border-w\": [\n                \"border-w-s\",\n                \"border-w-e\",\n                \"border-w-t\",\n                \"border-w-r\",\n                \"border-w-b\",\n                \"border-w-l\"\n            ],\n            \"border-w-x\": [\n                \"border-w-r\",\n                \"border-w-l\"\n            ],\n            \"border-w-y\": [\n                \"border-w-t\",\n                \"border-w-b\"\n            ],\n            \"border-color\": [\n                \"border-color-s\",\n                \"border-color-e\",\n                \"border-color-t\",\n                \"border-color-r\",\n                \"border-color-b\",\n                \"border-color-l\"\n            ],\n            \"border-color-x\": [\n                \"border-color-r\",\n                \"border-color-l\"\n            ],\n            \"border-color-y\": [\n                \"border-color-t\",\n                \"border-color-b\"\n            ],\n            \"scroll-m\": [\n                \"scroll-mx\",\n                \"scroll-my\",\n                \"scroll-ms\",\n                \"scroll-me\",\n                \"scroll-mt\",\n                \"scroll-mr\",\n                \"scroll-mb\",\n                \"scroll-ml\"\n            ],\n            \"scroll-mx\": [\n                \"scroll-mr\",\n                \"scroll-ml\"\n            ],\n            \"scroll-my\": [\n                \"scroll-mt\",\n                \"scroll-mb\"\n            ],\n            \"scroll-p\": [\n                \"scroll-px\",\n                \"scroll-py\",\n                \"scroll-ps\",\n                \"scroll-pe\",\n                \"scroll-pt\",\n                \"scroll-pr\",\n                \"scroll-pb\",\n                \"scroll-pl\"\n            ],\n            \"scroll-px\": [\n                \"scroll-pr\",\n                \"scroll-pl\"\n            ],\n            \"scroll-py\": [\n                \"scroll-pt\",\n                \"scroll-pb\"\n            ],\n            touch: [\n                \"touch-x\",\n                \"touch-y\",\n                \"touch-pz\"\n            ],\n            \"touch-x\": [\n                \"touch\"\n            ],\n            \"touch-y\": [\n                \"touch\"\n            ],\n            \"touch-pz\": [\n                \"touch\"\n            ]\n        },\n        conflictingClassGroupModifiers: {\n            \"font-size\": [\n                \"leading\"\n            ]\n        }\n    };\n};\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */ const mergeConfigs = (baseConfig, { cacheSize, prefix, separator, experimentalParseClassName, extend = {}, override = {} })=>{\n    overrideProperty(baseConfig, \"cacheSize\", cacheSize);\n    overrideProperty(baseConfig, \"prefix\", prefix);\n    overrideProperty(baseConfig, \"separator\", separator);\n    overrideProperty(baseConfig, \"experimentalParseClassName\", experimentalParseClassName);\n    for(const configKey in override){\n        overrideConfigProperties(baseConfig[configKey], override[configKey]);\n    }\n    for(const key in extend){\n        mergeConfigProperties(baseConfig[key], extend[key]);\n    }\n    return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue)=>{\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue;\n    }\n};\nconst overrideConfigProperties = (baseObject, overrideObject)=>{\n    if (overrideObject) {\n        for(const key in overrideObject){\n            overrideProperty(baseObject, key, overrideObject[key]);\n        }\n    }\n};\nconst mergeConfigProperties = (baseObject, mergeObject)=>{\n    if (mergeObject) {\n        for(const key in mergeObject){\n            const mergeValue = mergeObject[key];\n            if (mergeValue !== undefined) {\n                baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n            }\n        }\n    }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig)=>typeof configExtension === \"function\" ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(()=>mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/ createTailwindMerge(getDefaultConfig);\n //# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\n");

/***/ })

};
;