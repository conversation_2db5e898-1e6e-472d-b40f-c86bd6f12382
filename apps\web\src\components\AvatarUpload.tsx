'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Camera, Upload, X, User } from 'lucide-react'

interface AvatarUploadProps {
  currentAvatar?: string
  onAvatarChange?: (file: File | null) => void
  size?: 'sm' | 'md' | 'lg'
  editable?: boolean
}

export function AvatarUpload({ 
  currentAvatar, 
  onAvatarChange, 
  size = 'md', 
  editable = true 
}: AvatarUploadProps) {
  const [preview, setPreview] = useState<string | null>(currentAvatar || null)
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const sizeClasses = {
    sm: 'h-16 w-16',
    md: 'h-24 w-24',
    lg: 'h-32 w-32'
  }

  const iconSizes = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  }

  const buttonSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10'
  }

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setPreview(result)
        onAvatarChange?.(file)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleRemoveAvatar = () => {
    setPreview(null)
    onAvatarChange?.(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="relative">
      <div
        className={`relative mx-auto ${sizeClasses[size]} rounded-full overflow-hidden ${
          editable ? 'cursor-pointer' : ''
        } ${isDragging ? 'ring-2 ring-electric-500 ring-offset-2' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={editable ? openFileDialog : undefined}
      >
        {preview ? (
          <img
            src={preview}
            alt="Avatar"
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="h-full w-full bg-electric-100 dark:bg-electric-900 flex items-center justify-center">
            <User className={`${iconSizes[size]} text-electric-600`} />
          </div>
        )}
        
        {editable && (
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
            <Camera className="h-6 w-6 text-white opacity-0 hover:opacity-100 transition-opacity duration-200" />
          </div>
        )}
      </div>

      {editable && (
        <>
          {/* Camera/Upload Button */}
          <Button
            size="sm"
            onClick={openFileDialog}
            className={`absolute -bottom-2 -right-2 ${buttonSizes[size]} rounded-full bg-electric-600 hover:bg-electric-700 p-0`}
          >
            <Camera className="h-4 w-4" />
          </Button>

          {/* Remove Button (only show if there's a preview) */}
          {preview && (
            <Button
              size="sm"
              variant="destructive"
              onClick={handleRemoveAvatar}
              className={`absolute -top-2 -right-2 ${buttonSizes[size]} rounded-full p-0`}
            >
              <X className="h-3 w-3" />
            </Button>
          )}

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInput}
            className="hidden"
          />
        </>
      )}

      {/* Upload Instructions (for larger sizes) */}
      {editable && size === 'lg' && !preview && (
        <Card className="mt-4 dark:bg-gray-800 dark:border-gray-700">
          <CardContent className="p-4 text-center">
            <Upload className="mx-auto h-8 w-8 text-gray-400 dark:text-gray-500 mb-2" />
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
              Drag and drop an image here, or click to select
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Supports: JPG, PNG, GIF (max 5MB)
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
