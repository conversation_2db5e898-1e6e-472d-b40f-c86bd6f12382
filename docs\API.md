# API Documentation

This document describes the API structure and usage for the GreenMilesEV application.

## 🏗️ Architecture

The application uses Supabase as the backend, providing:
- **PostgreSQL Database** with Row Level Security (RLS)
- **REST API** auto-generated from database schema
- **Real-time subscriptions** for live updates
- **Authentication** with JWT tokens

## 🔐 Authentication

All API calls require authentication except for public charging station data.

### Authentication Flow

```typescript
import { supabase } from '@/lib/supabase'

// Sign up
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123',
  options: {
    data: {
      full_name: '<PERSON>'
    }
  }
})

// Sign in
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password123'
})

// Get current user
const { data: { user } } = await supabase.auth.getUser()
```

## 📊 Data Models

### Profile
```typescript
interface Profile {
  id: string              // UUID, matches auth.users.id
  email: string           // User email
  full_name?: string      // Display name
  avatar_url?: string     // Profile picture URL
  phone?: string          // Phone number
  address?: string        // Street address
  city?: string           // City
  state?: string          // State/Province
  zip_code?: string       // Postal code
  country: string         // Country (default: 'US')
  preferences: object     // User preferences JSON
  created_at: string      // ISO timestamp
  updated_at: string      // ISO timestamp
}
```

### Vehicle
```typescript
interface Vehicle {
  id: string                    // UUID
  user_id: string              // Foreign key to profiles
  make: string                 // Vehicle manufacturer
  model: string                // Vehicle model
  year: number                 // Model year
  trim?: string                // Trim level
  color?: string               // Vehicle color
  vin?: string                 // Vehicle identification number
  license_plate?: string       // License plate number
  battery_capacity_kwh: number // Battery capacity in kWh
  range_miles: number          // EPA estimated range
  efficiency_miles_per_kwh: number // Efficiency rating
  current_battery_level: number    // Current charge (0-100)
  current_range_miles: number      // Current estimated range
  odometer_miles: number           // Total miles driven
  is_primary: boolean              // Primary vehicle flag
  status: 'active' | 'inactive' | 'maintenance'
  metadata: object                 // Additional data JSON
  created_at: string              // ISO timestamp
  updated_at: string              // ISO timestamp
}
```

### Charging Station
```typescript
interface ChargingStation {
  id: string                  // UUID
  name: string               // Station name
  network: string            // Network (Tesla, ChargePoint, etc.)
  address: string            // Street address
  city: string               // City
  state: string              // State
  zip_code?: string          // Postal code
  country: string            // Country
  latitude: number           // GPS latitude
  longitude: number          // GPS longitude
  total_stalls: number       // Total charging stalls
  available_stalls: number   // Currently available stalls
  max_power_kw: number       // Maximum charging power
  connector_types: string[]  // Supported connector types
  pricing_per_kwh?: number   // Price per kWh
  pricing_per_minute?: number // Price per minute
  amenities: string[]        // Available amenities
  hours_of_operation?: string // Operating hours
  status: 'operational' | 'maintenance' | 'offline'
  last_updated: string       // Last data update
  created_at: string         // ISO timestamp
}
```

## 🔌 API Endpoints

### Profiles

#### Get Current User Profile
```typescript
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .single()
```

#### Update Profile
```typescript
const { data, error } = await supabase
  .from('profiles')
  .update({
    full_name: 'John Doe',
    phone: '+1234567890'
  })
  .eq('id', user.id)
  .select()
  .single()
```

### Vehicles

#### Get User Vehicles
```typescript
const { data, error } = await supabase
  .from('vehicles')
  .select('*')
  .order('created_at', { ascending: false })
```

#### Create Vehicle
```typescript
const { data, error } = await supabase
  .from('vehicles')
  .insert([{
    make: 'Tesla',
    model: 'Model 3',
    year: 2023,
    battery_capacity_kwh: 75,
    range_miles: 358
  }])
  .select()
  .single()
```

#### Update Vehicle
```typescript
const { data, error } = await supabase
  .from('vehicles')
  .update({
    current_battery_level: 85,
    current_range_miles: 247
  })
  .eq('id', vehicleId)
  .select()
  .single()
```

### Charging Stations

#### Get Charging Stations
```typescript
const { data, error } = await supabase
  .from('charging_stations')
  .select('*')
  .eq('status', 'operational')
  .limit(50)
```

#### Get Nearby Stations (with distance calculation)
```typescript
// Note: For production, use PostGIS for efficient geographic queries
const { data, error } = await supabase
  .from('charging_stations')
  .select('*')
  .eq('status', 'operational')

// Client-side distance filtering
const nearbyStations = data?.map(station => ({
  ...station,
  distance: calculateDistance(userLat, userLon, station.latitude, station.longitude)
})).filter(station => station.distance <= 25)
  .sort((a, b) => a.distance - b.distance)
```

### Charging Sessions

#### Get Charging Sessions
```typescript
const { data, error } = await supabase
  .from('charging_sessions')
  .select(`
    *,
    vehicle:vehicles(make, model, year),
    charging_station:charging_stations(name, network, address)
  `)
  .order('start_time', { ascending: false })
  .limit(50)
```

#### Create Charging Session
```typescript
const { data, error } = await supabase
  .from('charging_sessions')
  .insert([{
    vehicle_id: 'vehicle-uuid',
    charging_station_id: 'station-uuid',
    session_type: 'public',
    start_time: new Date().toISOString(),
    start_battery_level: 25,
    status: 'in_progress'
  }])
  .select()
  .single()
```

#### Complete Charging Session
```typescript
const { data, error } = await supabase
  .from('charging_sessions')
  .update({
    end_time: new Date().toISOString(),
    end_battery_level: 85,
    energy_added_kwh: 45.5,
    cost_total: 12.60,
    status: 'completed'
  })
  .eq('id', sessionId)
  .select()
  .single()
```

### Trips

#### Get Trips
```typescript
const { data, error } = await supabase
  .from('trips')
  .select(`
    *,
    vehicle:vehicles(make, model, year)
  `)
  .order('start_time', { ascending: false })
  .limit(50)
```

#### Create Trip
```typescript
const { data, error } = await supabase
  .from('trips')
  .insert([{
    vehicle_id: 'vehicle-uuid',
    start_location: 'Home',
    end_location: 'Office',
    start_time: new Date().toISOString(),
    start_battery_level: 85,
    status: 'in_progress'
  }])
  .select()
  .single()
```

## 🔄 Real-time Subscriptions

### Subscribe to Vehicle Updates
```typescript
const subscription = supabase
  .channel('vehicle-updates')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'vehicles',
    filter: `user_id=eq.${user.id}`
  }, (payload) => {
    console.log('Vehicle updated:', payload.new)
  })
  .subscribe()

// Cleanup
subscription.unsubscribe()
```

### Subscribe to Charging Session Updates
```typescript
const subscription = supabase
  .channel('charging-updates')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'charging_sessions',
    filter: `user_id=eq.${user.id}`
  }, (payload) => {
    console.log('Charging session update:', payload)
  })
  .subscribe()
```

## 🔒 Row Level Security (RLS)

All tables have RLS enabled with policies ensuring users can only access their own data:

### Profile Policies
- Users can view and update their own profile
- Automatic profile creation on signup

### Vehicle Policies
- Users can CRUD their own vehicles
- No access to other users' vehicles

### Session/Trip Policies
- Users can CRUD their own sessions and trips
- Data is completely isolated between users

### Charging Station Policies
- Public read access for all authenticated users
- Admin-only write access (for data management)

## 📈 Analytics Queries

### Vehicle Analytics
```typescript
// Get trip statistics for a vehicle
const { data: trips } = await supabase
  .from('trips')
  .select('distance_miles, energy_used_kwh, duration_minutes')
  .eq('vehicle_id', vehicleId)
  .eq('status', 'completed')
  .gte('start_time', startDate)
  .lte('start_time', endDate)

// Calculate efficiency, total miles, etc.
const totalMiles = trips.reduce((sum, trip) => sum + trip.distance_miles, 0)
const totalEnergy = trips.reduce((sum, trip) => sum + trip.energy_used_kwh, 0)
const averageEfficiency = totalMiles / totalEnergy
```

### Charging Analytics
```typescript
// Get charging cost analysis
const { data: sessions } = await supabase
  .from('charging_sessions')
  .select('cost_total, energy_added_kwh, session_type')
  .eq('vehicle_id', vehicleId)
  .eq('status', 'completed')
  .gte('start_time', startDate)

const totalCost = sessions.reduce((sum, session) => sum + session.cost_total, 0)
const totalEnergy = sessions.reduce((sum, session) => sum + session.energy_added_kwh, 0)
const averageCostPerKwh = totalCost / totalEnergy
```

## 🚨 Error Handling

```typescript
const { data, error } = await supabase
  .from('vehicles')
  .select('*')

if (error) {
  console.error('Database error:', error.message)
  // Handle specific error types
  if (error.code === 'PGRST116') {
    // No rows returned
  } else if (error.code === '42501') {
    // Insufficient privileges (RLS)
  }
}
```

## 🔧 Utility Functions

The shared package includes utility functions for common calculations:

```typescript
import { 
  calculateDistance,
  calculateEstimatedRange,
  calculateChargingTime,
  calculateEfficiency,
  formatCurrency,
  formatDuration
} from '@/shared/utils'

// Calculate distance between two points
const distance = calculateDistance(lat1, lon1, lat2, lon2)

// Calculate estimated range from battery level
const range = calculateEstimatedRange(batteryLevel, maxRange)

// Calculate charging time
const chargingTime = calculateChargingTime(
  currentLevel, 
  targetLevel, 
  batteryCapacity, 
  chargingPower
)
```
