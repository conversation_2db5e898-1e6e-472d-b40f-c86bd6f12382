import Link from 'next/link'
import { Zap } from 'lucide-react'

export function Footer() {
  return (
    <footer className="border-t bg-gray-50 py-12">
      <div className="container">
        <div className="flex flex-col items-center justify-between md:flex-row">
          <Link href="/" className="mb-4 flex items-center space-x-2 md:mb-0">
            <Zap className="h-5 w-5 text-electric-600" />
            <span className="font-semibold text-gray-900">GreenMilesEV</span>
          </Link>
          <p className="text-sm text-gray-600">
            © 2024 GreenMilesEV. All rights reserved. Building a sustainable future.
          </p>
        </div>
      </div>
    </footer>
  )
}
