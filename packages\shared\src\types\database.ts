// Database types for GreenMilesEV
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          phone: string | null
          address: string | null
          city: string | null
          state: string | null
          zip_code: string | null
          country: string
          preferences: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          zip_code?: string | null
          country?: string
          preferences?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          zip_code?: string | null
          country?: string
          preferences?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
      }
      vehicles: {
        Row: {
          id: string
          user_id: string
          make: string
          model: string
          year: number
          trim: string | null
          color: string | null
          vin: string | null
          license_plate: string | null
          battery_capacity_kwh: number
          range_miles: number
          efficiency_miles_per_kwh: number
          current_battery_level: number
          current_range_miles: number
          odometer_miles: number
          is_primary: boolean
          status: 'active' | 'inactive' | 'maintenance'
          metadata: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          make: string
          model: string
          year: number
          trim?: string | null
          color?: string | null
          vin?: string | null
          license_plate?: string | null
          battery_capacity_kwh: number
          range_miles: number
          efficiency_miles_per_kwh?: number
          current_battery_level?: number
          current_range_miles?: number
          odometer_miles?: number
          is_primary?: boolean
          status?: 'active' | 'inactive' | 'maintenance'
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          make?: string
          model?: string
          year?: number
          trim?: string | null
          color?: string | null
          vin?: string | null
          license_plate?: string | null
          battery_capacity_kwh?: number
          range_miles?: number
          efficiency_miles_per_kwh?: number
          current_battery_level?: number
          current_range_miles?: number
          odometer_miles?: number
          is_primary?: boolean
          status?: 'active' | 'inactive' | 'maintenance'
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
      }
      charging_stations: {
        Row: {
          id: string
          name: string
          network: string
          address: string
          city: string
          state: string
          zip_code: string | null
          country: string
          latitude: number
          longitude: number
          total_stalls: number
          available_stalls: number
          max_power_kw: number
          connector_types: string[]
          pricing_per_kwh: number | null
          pricing_per_minute: number | null
          amenities: string[]
          hours_of_operation: string | null
          status: 'operational' | 'maintenance' | 'offline'
          last_updated: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          network: string
          address: string
          city: string
          state: string
          zip_code?: string | null
          country?: string
          latitude: number
          longitude: number
          total_stalls?: number
          available_stalls?: number
          max_power_kw: number
          connector_types: string[]
          pricing_per_kwh?: number | null
          pricing_per_minute?: number | null
          amenities?: string[]
          hours_of_operation?: string | null
          status?: 'operational' | 'maintenance' | 'offline'
          last_updated?: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          network?: string
          address?: string
          city?: string
          state?: string
          zip_code?: string | null
          country?: string
          latitude?: number
          longitude?: number
          total_stalls?: number
          available_stalls?: number
          max_power_kw?: number
          connector_types?: string[]
          pricing_per_kwh?: number | null
          pricing_per_minute?: number | null
          amenities?: string[]
          hours_of_operation?: string | null
          status?: 'operational' | 'maintenance' | 'offline'
          last_updated?: string
          created_at?: string
        }
      }
      charging_sessions: {
        Row: {
          id: string
          user_id: string
          vehicle_id: string
          charging_station_id: string | null
          session_type: 'home' | 'public' | 'workplace'
          start_time: string
          end_time: string | null
          start_battery_level: number
          end_battery_level: number | null
          energy_added_kwh: number | null
          cost_total: number | null
          cost_per_kwh: number | null
          power_kw: number | null
          status: 'in_progress' | 'completed' | 'interrupted' | 'failed'
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          vehicle_id: string
          charging_station_id?: string | null
          session_type: 'home' | 'public' | 'workplace'
          start_time: string
          end_time?: string | null
          start_battery_level: number
          end_battery_level?: number | null
          energy_added_kwh?: number | null
          cost_total?: number | null
          cost_per_kwh?: number | null
          power_kw?: number | null
          status?: 'in_progress' | 'completed' | 'interrupted' | 'failed'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          vehicle_id?: string
          charging_station_id?: string | null
          session_type?: 'home' | 'public' | 'workplace'
          start_time?: string
          end_time?: string | null
          start_battery_level?: number
          end_battery_level?: number | null
          energy_added_kwh?: number | null
          cost_total?: number | null
          cost_per_kwh?: number | null
          power_kw?: number | null
          status?: 'in_progress' | 'completed' | 'interrupted' | 'failed'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      trips: {
        Row: {
          id: string
          user_id: string
          vehicle_id: string
          start_location: string
          end_location: string
          start_latitude: number | null
          start_longitude: number | null
          end_latitude: number | null
          end_longitude: number | null
          start_time: string
          end_time: string | null
          distance_miles: number | null
          duration_minutes: number | null
          start_battery_level: number | null
          end_battery_level: number | null
          energy_used_kwh: number | null
          efficiency_miles_per_kwh: number | null
          average_speed_mph: number | null
          weather_conditions: string | null
          trip_type: 'commute' | 'leisure' | 'business' | 'other' | null
          notes: string | null
          status: 'in_progress' | 'completed' | 'cancelled'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          vehicle_id: string
          start_location: string
          end_location: string
          start_latitude?: number | null
          start_longitude?: number | null
          end_latitude?: number | null
          end_longitude?: number | null
          start_time: string
          end_time?: string | null
          distance_miles?: number | null
          duration_minutes?: number | null
          start_battery_level?: number | null
          end_battery_level?: number | null
          energy_used_kwh?: number | null
          efficiency_miles_per_kwh?: number | null
          average_speed_mph?: number | null
          weather_conditions?: string | null
          trip_type?: 'commute' | 'leisure' | 'business' | 'other' | null
          notes?: string | null
          status?: 'in_progress' | 'completed' | 'cancelled'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          vehicle_id?: string
          start_location?: string
          end_location?: string
          start_latitude?: number | null
          start_longitude?: number | null
          end_latitude?: number | null
          end_longitude?: number | null
          start_time?: string
          end_time?: string | null
          distance_miles?: number | null
          duration_minutes?: number | null
          start_battery_level?: number | null
          end_battery_level?: number | null
          energy_used_kwh?: number | null
          efficiency_miles_per_kwh?: number | null
          average_speed_mph?: number | null
          weather_conditions?: string | null
          trip_type?: 'commute' | 'leisure' | 'business' | 'other' | null
          notes?: string | null
          status?: 'in_progress' | 'completed' | 'cancelled'
          created_at?: string
          updated_at?: string
        }
      }
      maintenance_records: {
        Row: {
          id: string
          user_id: string
          vehicle_id: string
          service_type: string
          description: string
          service_date: string
          odometer_miles: number | null
          cost: number | null
          service_provider: string | null
          next_service_miles: number | null
          next_service_date: string | null
          status: 'scheduled' | 'completed' | 'overdue'
          notes: string | null
          attachments: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          vehicle_id: string
          service_type: string
          description: string
          service_date: string
          odometer_miles?: number | null
          cost?: number | null
          service_provider?: string | null
          next_service_miles?: number | null
          next_service_date?: string | null
          status?: 'scheduled' | 'completed' | 'overdue'
          notes?: string | null
          attachments?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          vehicle_id?: string
          service_type?: string
          description?: string
          service_date?: string
          odometer_miles?: number | null
          cost?: number | null
          service_provider?: string | null
          next_service_miles?: number | null
          next_service_date?: string | null
          status?: 'scheduled' | 'completed' | 'overdue'
          notes?: string | null
          attachments?: string[]
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
