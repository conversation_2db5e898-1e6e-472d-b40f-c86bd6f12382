
> @supabase/auth-helpers-nextjs@0.8.7 build /home/<USER>/work/auth-helpers/auth-helpers/packages/nextjs
> tsup

[34mCLI[39m Building entry: src/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v6.7.0
[34mCLI[39m Using tsup config: /home/<USER>/work/auth-helpers/auth-helpers/packages/nextjs/tsup.config.ts
[34mCLI[39m Target: node14
[34mCLI[39m Cleaning output folder
[34mCJS[39m Build start
[32mCJS[39m [1mdist/index.js     [22m[32m12.58 KB[39m
[32mCJS[39m [1mdist/index.js.map [22m[32m28.99 KB[39m
[32mCJS[39m ⚡️ Build success in 30ms
[34mDTS[39m Build start
[32mDTS[39m ⚡️ Build success in 5820ms
[32mDTS[39m [1mdist/index.d.ts [22m[32m5.95 KB[39m
