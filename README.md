# GreenMilesEV

A full-stack electric vehicle management platform built with modern web and mobile technologies.

## 🚗 Project Overview

GreenMilesEV is a comprehensive platform for electric vehicle management, featuring:

- **Web Application**: Next.js with TypeScript, Tailwind CSS, and shadcn/ui
- **Mobile Application**: React Native with Expo
- **Backend**: Supabase for authentication, database, and API
- **Shared Code**: Common types and utilities across platforms

## 📁 Project Structure

```
greenmiles-ev/
├── apps/
│   ├── web/          # Next.js web application
│   └── mobile/       # React Native mobile app
├── packages/
│   └── shared/       # Shared types and utilities
├── docs/             # Documentation
└── package.json      # Root package.json with workspace configuration
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Git
- For mobile development: Expo CLI (`npm install -g @expo/cli`)
- Supabase account (free tier available)

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd greenmiles-ev
```

2. Install dependencies for all projects:

```bash
npm install
npm run install:all
```

3. Set up environment variables:

```bash
# Interactive setup (recommended)
node scripts/setup-env.js

# Or manually copy and edit
cp apps/web/.env.local.example apps/web/.env.local
cp apps/mobile/.env.example apps/mobile/.env
```

4. Get your Supabase credentials:

   - Go to [Supabase Dashboard](https://supabase.com/dashboard/project/pbevpexclffmhqstwlha/settings/api)
   - Copy your Project URL and Anon Key
   - Update the environment files

5. Start development servers:

```bash
# Start both web and mobile
npm run dev

# Or start individually
npm run dev:web
npm run dev:mobile
```

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start both web and mobile development servers
- `npm run build` - Build both applications
- `npm run lint` - Run linting for all projects
- `npm run type-check` - Run TypeScript type checking
- `npm run clean` - Clean all node_modules

### Individual Applications

- **Web App**: See `apps/web/README.md`
- **Mobile App**: See `apps/mobile/README.md`
- **Shared Packages**: See `packages/shared/README.md`

## 🏗️ Tech Stack

### Frontend

- **Web**: Next.js 14+, TypeScript, Tailwind CSS, shadcn/ui
- **Mobile**: React Native, Expo, TypeScript, NativeWind

### Backend

- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with RLS
- **API**: Supabase REST API & Real-time subscriptions
- **Storage**: Supabase Storage (for future file uploads)

### Development Tools

- TypeScript for type safety across all platforms
- ESLint & Prettier for consistent code quality
- Monorepo structure with shared packages
- Environment configuration scripts

### Key Features

- **Cross-platform**: Shared business logic and types
- **Real-time**: Live updates for charging status and trips
- **Secure**: Row Level Security (RLS) for data protection
- **Scalable**: Modular architecture with clean separation

## 📱 Features

- User authentication and profiles
- Electric vehicle management
- Charging station locator
- Trip planning and tracking
- Energy consumption analytics
- Real-time notifications

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
