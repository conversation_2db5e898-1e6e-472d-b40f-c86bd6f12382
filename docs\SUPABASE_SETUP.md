# Supabase Backend Setup Guide

This guide will help you set up the Supabase backend for the GreenMilesEV project.

## 🗄️ Database Schema

The GreenMilesEV database includes the following tables:

### Core Tables

1. **profiles** - User profile information extending Supabase auth
2. **vehicles** - User vehicle information and specifications
3. **charging_stations** - Public charging station data
4. **charging_sessions** - User charging history and sessions
5. **trips** - Trip tracking and analytics
6. **maintenance_records** - Vehicle maintenance tracking

## 🔧 Current Setup Status

✅ **Database Schema Created**
- All tables created with proper relationships
- Row Level Security (RLS) enabled
- Indexes created for performance
- Triggers for automatic timestamps

✅ **Authentication Configured**
- Email/password authentication enabled
- Google and GitHub OAuth enabled (requires setup)
- Automatic profile creation on signup
- JWT token expiration: 1 hour

✅ **Sample Data Inserted**
- 5 sample charging stations in California
- Ready for testing and development

## 🔑 Getting Your API Keys

1. **Go to your Supabase project**: https://supabase.com/dashboard/project/pbevpexclffmhqstwlha

2. **Get your API keys**:
   - Go to Settings → API
   - Copy the `Project URL` and `anon public` key
   - Optionally copy the `service_role` key for server-side operations

3. **Update environment files**:
   ```bash
   # For web app
   cp apps/web/.env.local.example apps/web/.env.local
   
   # For mobile app  
   cp apps/mobile/.env.example apps/mobile/.env
   ```

4. **Fill in your actual values**:
   ```env
   # Web app (.env.local)
   NEXT_PUBLIC_SUPABASE_URL=https://pbevpexclffmhqstwlha.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here
   
   # Mobile app (.env)
   EXPO_PUBLIC_SUPABASE_URL=https://pbevpexclffmhqstwlha.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here
   ```

## 🔐 Row Level Security (RLS) Policies

All tables have RLS enabled with the following policies:

### Profiles
- Users can view/update their own profile
- Automatic profile creation on signup

### Vehicles
- Users can CRUD their own vehicles
- Vehicle data is private to each user

### Charging Sessions
- Users can CRUD their own charging sessions
- Session data is private to each user

### Trips
- Users can CRUD their own trips
- Trip data is private to each user

### Maintenance Records
- Users can CRUD their own maintenance records
- Maintenance data is private to each user

### Charging Stations
- Public read access for all authenticated users
- Admin-only write access (for future admin panel)

## 🚀 Testing the Setup

1. **Start your web app**:
   ```bash
   cd apps/web
   npm run dev
   ```

2. **Test authentication**:
   - Try signing up with email/password
   - Check if profile is automatically created
   - Verify RLS policies are working

3. **Test data operations**:
   - Add a vehicle
   - View charging stations
   - Create test trips and charging sessions

## 📊 Sample Charging Stations

The database includes 5 sample charging stations:

1. **Tesla Supercharger - Downtown Mall** (12 stalls, 8 available)
2. **ChargePoint - City Center** (6 stalls, 2 available)
3. **Electrify America - Highway Plaza** (8 stalls, 6 available)
4. **EVgo - Shopping Center** (4 stalls, 3 available)
5. **Blink - Office Complex** (10 stalls, 7 available)

## 🔄 Database Functions

### Automatic Profile Creation
- `handle_new_user()` - Creates profile when user signs up
- Triggered automatically on auth.users INSERT

### Timestamp Updates
- `handle_updated_at()` - Updates updated_at field
- Applied to all tables with updated_at column

## 📈 Performance Optimizations

### Indexes Created
- Location-based queries on charging_stations
- User-specific queries on all user tables
- Time-based queries for trips and charging sessions

### Query Optimization
- Proper foreign key relationships
- Efficient RLS policies
- Optimized for common query patterns

## 🛠️ Next Steps

1. **Set up OAuth providers** (optional):
   - Configure Google OAuth in Supabase dashboard
   - Configure GitHub OAuth in Supabase dashboard

2. **Add real charging station data**:
   - Import data from charging networks APIs
   - Set up automated data updates

3. **Configure email templates**:
   - Customize signup confirmation emails
   - Set up password reset emails

4. **Set up monitoring**:
   - Enable database monitoring
   - Set up alerts for performance issues

## 🔍 Troubleshooting

### Common Issues

1. **RLS Policy Errors**:
   - Make sure user is authenticated
   - Check if policies allow the operation
   - Verify user_id matches auth.uid()

2. **Connection Issues**:
   - Verify API keys are correct
   - Check if project is active
   - Ensure environment variables are loaded

3. **Performance Issues**:
   - Check if indexes are being used
   - Monitor query performance in dashboard
   - Optimize complex queries

### Useful SQL Queries

```sql
-- Check if user profile exists
SELECT * FROM profiles WHERE id = auth.uid();

-- View user's vehicles
SELECT * FROM vehicles WHERE user_id = auth.uid();

-- Check charging stations near location
SELECT *, 
  (6371 * acos(cos(radians(YOUR_LAT)) * cos(radians(latitude)) * 
  cos(radians(longitude) - radians(YOUR_LON)) + sin(radians(YOUR_LAT)) * 
  sin(radians(latitude)))) AS distance
FROM charging_stations 
ORDER BY distance LIMIT 10;
```

## 📞 Support

If you encounter issues:
1. Check the Supabase dashboard for errors
2. Review the database logs
3. Verify your environment configuration
4. Test with the Supabase SQL editor
