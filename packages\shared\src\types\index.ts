// Export all types
export * from './database'

// Common types used across the application
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
}

export interface Vehicle {
  id: string
  user_id: string
  make: string
  model: string
  year: number
  trim?: string
  color?: string
  vin?: string
  license_plate?: string
  battery_capacity_kwh: number
  range_miles: number
  efficiency_miles_per_kwh: number
  current_battery_level: number
  current_range_miles: number
  odometer_miles: number
  is_primary: boolean
  status: 'active' | 'inactive' | 'maintenance'
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ChargingStation {
  id: string
  name: string
  network: string
  address: string
  city: string
  state: string
  zip_code?: string
  country: string
  latitude: number
  longitude: number
  total_stalls: number
  available_stalls: number
  max_power_kw: number
  connector_types: string[]
  pricing_per_kwh?: number
  pricing_per_minute?: number
  amenities: string[]
  hours_of_operation?: string
  status: 'operational' | 'maintenance' | 'offline'
  last_updated: string
  created_at: string
}

export interface ChargingSession {
  id: string
  user_id: string
  vehicle_id: string
  charging_station_id?: string
  session_type: 'home' | 'public' | 'workplace'
  start_time: string
  end_time?: string
  start_battery_level: number
  end_battery_level?: number
  energy_added_kwh?: number
  cost_total?: number
  cost_per_kwh?: number
  power_kw?: number
  status: 'in_progress' | 'completed' | 'interrupted' | 'failed'
  notes?: string
  created_at: string
  updated_at: string
}

export interface Trip {
  id: string
  user_id: string
  vehicle_id: string
  start_location: string
  end_location: string
  start_latitude?: number
  start_longitude?: number
  end_latitude?: number
  end_longitude?: number
  start_time: string
  end_time?: string
  distance_miles?: number
  duration_minutes?: number
  start_battery_level?: number
  end_battery_level?: number
  energy_used_kwh?: number
  efficiency_miles_per_kwh?: number
  average_speed_mph?: number
  weather_conditions?: string
  trip_type?: 'commute' | 'leisure' | 'business' | 'other'
  notes?: string
  status: 'in_progress' | 'completed' | 'cancelled'
  created_at: string
  updated_at: string
}

export interface MaintenanceRecord {
  id: string
  user_id: string
  vehicle_id: string
  service_type: string
  description: string
  service_date: string
  odometer_miles?: number
  cost?: number
  service_provider?: string
  next_service_miles?: number
  next_service_date?: string
  status: 'scheduled' | 'completed' | 'overdue'
  notes?: string
  attachments: string[]
  created_at: string
  updated_at: string
}

// API Response types
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  count: number
  page: number
  limit: number
  total_pages: number
}

// Location types
export interface Location {
  latitude: number
  longitude: number
}

export interface Address {
  street: string
  city: string
  state: string
  zip_code: string
  country: string
}

// Analytics types
export interface VehicleAnalytics {
  total_miles: number
  total_trips: number
  average_efficiency: number
  total_energy_used: number
  total_charging_cost: number
  carbon_saved_lbs: number
  money_saved: number
}

export interface ChargingAnalytics {
  total_sessions: number
  total_energy: number
  total_cost: number
  average_session_duration: number
  favorite_networks: string[]
}

// Form types
export interface VehicleFormData {
  make: string
  model: string
  year: number
  trim?: string
  color?: string
  vin?: string
  license_plate?: string
  battery_capacity_kwh: number
  range_miles: number
  efficiency_miles_per_kwh?: number
}

export interface ProfileFormData {
  full_name?: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  country?: string
}

// Constants
export const VEHICLE_MAKES = [
  'Tesla',
  'BMW',
  'Audi',
  'Mercedes-Benz',
  'Volkswagen',
  'Nissan',
  'Chevrolet',
  'Ford',
  'Hyundai',
  'Kia',
  'Volvo',
  'Polestar',
  'Lucid',
  'Rivian',
  'Other'
] as const

export const CHARGING_NETWORKS = [
  'Tesla Supercharger',
  'ChargePoint',
  'Electrify America',
  'EVgo',
  'Blink',
  'Volta',
  'Greenlots',
  'Other'
] as const

export const CONNECTOR_TYPES = [
  'Tesla',
  'CCS',
  'CHAdeMO',
  'Type 2',
  'J1772'
] as const

export const TRIP_TYPES = [
  'commute',
  'leisure',
  'business',
  'other'
] as const

export const SERVICE_TYPES = [
  'tire_rotation',
  'brake_inspection',
  'software_update',
  'battery_check',
  'general_inspection',
  'recall_service',
  'warranty_repair',
  'other'
] as const
