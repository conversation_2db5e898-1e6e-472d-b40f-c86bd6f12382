/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?64b7\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q2FwcHMlNUN3ZWIlNUNzcmMlNUNhcHAlNUNkYXNoYm9hcmQlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvP2IxYzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxvYmlfY1xcXFxEZXNrdG9wXFxcXGV2LWFwcFxcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AuthErrorBoundary.tsx */ \"(ssr)/./src/components/AuthErrorBoundary.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q2FwcHMlNUN3ZWIlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q29iaV9jJTVDRGVza3RvcCU1Q2V2LWFwcCU1Q2FwcHMlNUN3ZWIlNUNzcmMlNUNjb21wb25lbnRzJTVDQXV0aEVycm9yQm91bmRhcnkudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDb2JpX2MlNUNEZXNrdG9wJTVDZXYtYXBwJTVDYXBwcyU1Q3dlYiU1Q3NyYyU1Q2NvbnRleHRzJTVDQXV0aENvbnRleHQudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDb2JpX2MlNUNEZXNrdG9wJTVDZXYtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBeUg7QUFDekgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvPzI3ZDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxvYmlfY1xcXFxEZXNrdG9wXFxcXGV2LWFwcFxcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxBdXRoRXJyb3JCb3VuZGFyeS50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG9iaV9jXFxcXERlc2t0b3BcXFxcZXYtYXBwXFxcXGFwcHNcXFxcd2ViXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccomponents%5CAuthErrorBoundary.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,MapPin,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,MapPin,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,MapPin,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,MapPin,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Car,MapPin,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction DashboardContent() {\n    const { user, profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                variant: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"mb-2 text-3xl font-bold text-gray-900\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Welcome back! Here's an overview of your electric vehicle activity.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Battery Level\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 text-electric-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-electric-600\",\n                                                children: \"85%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"247 miles range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"This Month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"847\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"miles driven\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Charging Cost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"$42\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"this month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Carbon Saved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-green-100 text-green-800\",\n                                                children: \"127 lbs CO₂\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: \"$89\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"money saved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 grid grid-cols-1 gap-8 lg:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Common tasks and shortcuts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                className: \"h-20 flex-col bg-electric-600 hover:bg-electric-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mb-2 h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Find Charging\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mb-2 h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"My Vehicle\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mb-2 h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Analytics\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mb-2 h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                children: \"Your latest trips and charging sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"rounded-full bg-electric-100 p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4 text-electric-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Charging Complete\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Tesla Supercharger - Downtown\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"2h ago\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"rounded-full bg-blue-100 p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Trip Completed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Home to Office - 24.5 miles\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"5h ago\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"rounded-full bg-green-100 p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Car_MapPin_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Weekly Report\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"4.2 mi/kWh efficiency\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"1d ago\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        children: \"Vehicle Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                        children: \"Current status of your Tesla Model 3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-6 md:grid-cols-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 text-3xl font-bold text-electric-600\",\n                                                    children: \"85%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Battery Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 h-2 w-full rounded-full bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2 rounded-full bg-electric-600\",\n                                                        style: {\n                                                            width: \"85%\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 text-3xl font-bold text-gray-900\",\n                                                    children: \"247\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Miles Range\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-xs text-gray-500\",\n                                                    children: \"Estimated remaining\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-2 text-3xl font-bold text-gray-900\",\n                                                    children: \"Parked\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-2 text-xs text-gray-500\",\n                                                    children: \"Home garage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_2__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthErrorBoundary: () => (/* binding */ AuthErrorBoundary),\n/* harmony export */   useAuthErrorHandler: () => (/* binding */ useAuthErrorHandler)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ AuthErrorBoundary,useAuthErrorHandler auto */ \n\n\n\n\nclass AuthErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"Authentication error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                lineNumber: 42,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, retry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertTitle, {\n                            children: \"Authentication Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertDescription, {\n                            className: \"mt-2\",\n                            children: error.message || \"An unexpected error occurred during authentication.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: retry,\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"flex-1\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\AuthErrorBoundary.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n// Hook for handling auth errors in functional components\nfunction useAuthErrorHandler() {\n    const handleAuthError = (error)=>{\n        console.error(\"Auth error:\", error);\n        // Handle specific error types\n        if (error?.message?.includes(\"Invalid login credentials\")) {\n            return \"Invalid email or password. Please check your credentials and try again.\";\n        }\n        if (error?.message?.includes(\"Email not confirmed\")) {\n            return \"Please check your email and click the confirmation link before signing in.\";\n        }\n        if (error?.message?.includes(\"Too many requests\")) {\n            return \"Too many login attempts. Please wait a few minutes before trying again.\";\n        }\n        if (error?.message?.includes(\"Network\")) {\n            return \"Network error. Please check your internet connection and try again.\";\n        }\n        // Default error message\n        return error?.message || \"An unexpected error occurred. Please try again.\";\n    };\n    return {\n        handleAuthError\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,X,Zap!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\nfunction Header({ variant = \"default\" }) {\n    const { user, profile, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const handleSignOut = async ()=>{\n        await signOut();\n    };\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    // Navigation links for authenticated users\n    const authenticatedNavLinks = [\n        {\n            href: \"/dashboard\",\n            label: \"Dashboard\"\n        },\n        {\n            href: \"/vehicles\",\n            label: \"My Vehicles\"\n        },\n        {\n            href: \"/charging\",\n            label: \"Charging\"\n        },\n        {\n            href: \"/analytics\",\n            label: \"Analytics\"\n        }\n    ];\n    // Navigation links for public pages\n    const publicNavLinks = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/#features\",\n            label: \"Features\"\n        },\n        {\n            href: \"/#about\",\n            label: \"About\"\n        },\n        {\n            href: \"/#contact\",\n            label: \"Contact\"\n        }\n    ];\n    const navLinks = user ? authenticatedNavLinks : publicNavLinks;\n    if (variant === \"dashboard\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"bg-white border-b\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: user ? \"/dashboard\" : \"/\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-electric-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"GreenMilesEV\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex items-center space-x-6\",\n                                children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: link.href,\n                                        className: `text-sm font-medium transition-colors ${isActive(link.href) ? \"text-electric-600\" : \"text-gray-600 hover:text-electric-600\"}`,\n                                        children: link.label\n                                    }, link.href, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:block text-sm text-gray-600\",\n                                                children: [\n                                                    \"Welcome, \",\n                                                    profile?.full_name || user?.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/settings\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: handleSignOut,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/auth/signin\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"sm\",\n                                                className: \"bg-electric-600 hover:bg-electric-700\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/auth/signup\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"md:hidden\",\n                                        onClick: toggleMobileMenu,\n                                        children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this),\n                    isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden border-t bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"px-4 py-4 space-y-2\",\n                            children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.href,\n                                    className: `block px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive(link.href) ? \"text-electric-600 bg-electric-50\" : \"text-gray-600 hover:text-electric-600 hover:bg-gray-50\"}`,\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: link.label\n                                }, link.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    // Default header for public pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container flex h-16 items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-electric-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"GreenMilesEV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: link.href,\n                                className: `text-sm font-medium transition-colors ${isActive(link.href) ? \"text-electric-600\" : \"text-gray-600 hover:text-electric-600\"}`,\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleSignOut,\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/auth/signin\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"sm\",\n                                        className: \"bg-electric-600 hover:bg-electric-700\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/auth/signup\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"md:hidden\",\n                                onClick: toggleMobileMenu,\n                                children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden border-t bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container px-4 py-4 space-y-2\",\n                    children: navLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: link.href,\n                            className: `block px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive(link.href) ? \"text-electric-600 bg-electric-50\" : \"text-gray-600 hover:text-electric-600 hover:bg-gray-50\"}`,\n                            onClick: ()=>setIsMobileMenuOpen(false),\n                            children: link.label\n                        }, link.href, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute),\n/* harmony export */   withProtectedRoute: () => (/* binding */ withProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute,withProtectedRoute auto */ \n\n\n\n\nfunction ProtectedRoute({ children, redirectTo = \"/auth/signin\" }) {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user) {\n            router.push(redirectTo);\n        }\n    }, [\n        user,\n        loading,\n        router,\n        redirectTo\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin text-electric-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null // Will redirect in useEffect\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Higher-order component version\nfunction withProtectedRoute(Component, redirectTo) {\n    return function ProtectedComponent(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            redirectTo: redirectTo,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            if (error) {\n                console.error(\"Error getting session:\", error);\n            }\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchProfile();\n            }\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.email);\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                await fetchProfile();\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const fetchProfile = async ()=>{\n        try {\n            const { data, error } = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.getProfile();\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n            } else {\n                setProfile(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n        }\n    };\n    const signUp = async (email, password, metadata)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: metadata\n                }\n            });\n            return {\n                data,\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            return {\n                data,\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            return {\n                error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/reset-password`\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGoogle = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"google\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signInWithGitHub = async ()=>{\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithOAuth({\n            provider: \"github\",\n            options: {\n                redirectTo: `${window.location.origin}/dashboard`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const updateProfile = async (updates)=>{\n        try {\n            const { data, error } = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.profileApi.updateProfile(updates);\n            if (data && !error) {\n                setProfile(data);\n            }\n            return {\n                data,\n                error\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    };\n    const refreshProfile = async ()=>{\n        await fetchProfile();\n    };\n    const value = {\n        user,\n        profile,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        signInWithGoogle,\n        signInWithGitHub,\n        updateProfile,\n        refreshProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 193,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protected routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex min-h-screen items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 w-32 animate-spin rounded-full border-b-2 border-electric-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login page\n            window.location.href = \"/auth/signin\";\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 223,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   chargingSessionApi: () => (/* binding */ chargingSessionApi),\n/* harmony export */   chargingStationApi: () => (/* binding */ chargingStationApi),\n/* harmony export */   maintenanceApi: () => (/* binding */ maintenanceApi),\n/* harmony export */   profileApi: () => (/* binding */ profileApi),\n/* harmony export */   tripApi: () => (/* binding */ tripApi),\n/* harmony export */   vehicleApi: () => (/* binding */ vehicleApi)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// Profile API\nconst profileApi = {\n    async getProfile () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").select(\"*\").single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateProfile (updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"profiles\").update(updates).eq(\"id\", (await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser()).data.user?.id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Vehicle API\nconst vehicleApi = {\n    async getVehicles () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        return {\n            data,\n            error\n        };\n    },\n    async getVehicle (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").select(\"*\").eq(\"id\", id).single();\n        return {\n            data,\n            error\n        };\n    },\n    async createVehicle (vehicle) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").insert([\n            vehicle\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateVehicle (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async deleteVehicle (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"vehicles\").delete().eq(\"id\", id);\n        return {\n            error\n        };\n    }\n};\n// Charging Station API\nconst chargingStationApi = {\n    async getChargingStations (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\").limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async getNearbyStations (latitude, longitude, radiusMiles = 25) {\n        // Using PostGIS extension for geographic queries (if available)\n        // For now, we'll fetch all stations and filter client-side\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_stations\").select(\"*\").eq(\"status\", \"operational\");\n        if (data && !error) {\n            // Simple distance calculation (for production, use PostGIS)\n            const stationsWithDistance = data.map((station)=>({\n                    ...station,\n                    distance: calculateDistance(latitude, longitude, station.latitude, station.longitude)\n                })).filter((station)=>station.distance <= radiusMiles).sort((a, b)=>a.distance - b.distance);\n            return {\n                data: stationsWithDistance,\n                error\n            };\n        }\n        return {\n            data,\n            error\n        };\n    }\n};\n// Charging Session API\nconst chargingSessionApi = {\n    async getChargingSessions (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(`\n        *,\n        vehicle:vehicles(make, model, year),\n        charging_station:charging_stations(name, network, address)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createChargingSession (session) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").insert([\n            session\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateChargingSession (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Trip API\nconst tripApi = {\n    async getTrips (limit = 50) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"start_time\", {\n            ascending: false\n        }).limit(limit);\n        return {\n            data,\n            error\n        };\n    },\n    async createTrip (trip) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").insert([\n            trip\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateTrip (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Maintenance API\nconst maintenanceApi = {\n    async getMaintenanceRecords (vehicleId) {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").select(`\n        *,\n        vehicle:vehicles(make, model, year)\n      `).order(\"service_date\", {\n            ascending: false\n        });\n        if (vehicleId) {\n            query = query.eq(\"vehicle_id\", vehicleId);\n        }\n        const { data, error } = await query;\n        return {\n            data,\n            error\n        };\n    },\n    async createMaintenanceRecord (record) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").insert([\n            record\n        ]).select().single();\n        return {\n            data,\n            error\n        };\n    },\n    async updateMaintenanceRecord (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"maintenance_records\").update(updates).eq(\"id\", id).select().single();\n        return {\n            data,\n            error\n        };\n    }\n};\n// Analytics API\nconst analyticsApi = {\n    async getVehicleAnalytics (vehicleId, startDate, endDate) {\n        // Get trips for the vehicle\n        let tripsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"trips\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) tripsQuery = tripsQuery.gte(\"start_time\", startDate);\n        if (endDate) tripsQuery = tripsQuery.lte(\"start_time\", endDate);\n        const { data: trips, error: tripsError } = await tripsQuery;\n        // Get charging sessions for the vehicle\n        let sessionsQuery = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"charging_sessions\").select(\"*\").eq(\"vehicle_id\", vehicleId).eq(\"status\", \"completed\");\n        if (startDate) sessionsQuery = sessionsQuery.gte(\"start_time\", startDate);\n        if (endDate) sessionsQuery = sessionsQuery.lte(\"start_time\", endDate);\n        const { data: sessions, error: sessionsError } = await sessionsQuery;\n        if (tripsError || sessionsError) {\n            return {\n                data: null,\n                error: tripsError || sessionsError\n            };\n        }\n        // Calculate analytics\n        const totalMiles = trips?.reduce((sum, trip)=>sum + (trip.distance_miles || 0), 0) || 0;\n        const totalTrips = trips?.length || 0;\n        const totalEnergyUsed = trips?.reduce((sum, trip)=>sum + (trip.energy_used_kwh || 0), 0) || 0;\n        const totalChargingCost = sessions?.reduce((sum, session)=>sum + (session.cost_total || 0), 0) || 0;\n        const averageEfficiency = totalEnergyUsed > 0 ? totalMiles / totalEnergyUsed : 0;\n        // Estimate carbon and money savings (compared to average gas car)\n        const carbonSavedLbs = totalMiles * 0.89 // Rough estimate: 0.89 lbs CO2 per mile for gas car\n        ;\n        const moneySaved = totalMiles / 25 * 3.50 - totalChargingCost // Assume 25 MPG, $3.50/gallon\n        ;\n        const analytics = {\n            total_miles: Math.round(totalMiles * 10) / 10,\n            total_trips: totalTrips,\n            average_efficiency: Math.round(averageEfficiency * 100) / 100,\n            total_energy_used: Math.round(totalEnergyUsed * 10) / 10,\n            total_charging_cost: Math.round(totalChargingCost * 100) / 100,\n            carbon_saved_lbs: Math.round(carbonSavedLbs * 10) / 10,\n            money_saved: Math.round(moneySaved * 100) / 100\n        };\n        return {\n            data: analytics,\n            error: null\n        };\n    }\n};\n// Helper function for distance calculation\nfunction calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 3959 // Earth's radius in miles\n    ;\n    const dLat = toRadians(lat2 - lat1);\n    const dLon = toRadians(lon2 - lon1);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\nfunction toRadians(degrees) {\n    return degrees * (Math.PI / 180);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://pbevpexclffmhqstwlha.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZXZwZXhjbGZmbWhxc3R3bGhhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjA1MzksImV4cCI6MjA2NTQ5NjUzOX0.F6bMKoeKV5QTyt0oEfNDn6-s780wkvvddplEBUuRpsI\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBR3BELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLFdBQVdSLG1FQUFZQSxDQUFXQyxhQUFhSSxpQkFBaUI7SUFDM0VJLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ3JlZW5taWxlcy1ldi93ZWIvLi9zcmMvbGliL3N1cGFiYXNlLnRzPzA2ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gJ0Avc2hhcmVkL3R5cGVzJ1xuXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCFcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuXG5pZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUFub25LZXkpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFN1cGFiYXNlIGVudmlyb25tZW50IHZhcmlhYmxlcycpXG59XG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudDxEYXRhYmFzZT4oc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSwge1xuICBhdXRoOiB7XG4gICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gIH0sXG59KVxuXG4vLyBFeHBvcnQgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnQC9zaGFyZWQvdHlwZXMnXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJFcnJvciIsInN1cGFiYXNlIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImRldGVjdFNlc3Npb25JblVybCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BncmVlbm1pbGVzLWV2L3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"067048cfb679\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGdyZWVubWlsZXMtZXYvd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz81YmI5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDY3MDQ4Y2ZiNjc5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\app\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthErrorBoundary */ \"(rsc)/./src/components/AuthErrorBoundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"GreenMilesEV - Electric Vehicle Management\",\n    description: \"Comprehensive platform for electric vehicle management, charging, and trip planning\",\n    keywords: [\n        \"electric vehicle\",\n        \"EV\",\n        \"charging\",\n        \"green energy\",\n        \"sustainability\"\n    ],\n    authors: [\n        {\n            name: \"GreenMilesEV Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthErrorBoundary__WEBPACK_IMPORTED_MODULE_3__.AuthErrorBoundary, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-background\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ev-app\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AuthErrorBoundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/AuthErrorBoundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useAuthErrorHandler: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#AuthErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\components\AuthErrorBoundary.tsx#useAuthErrorHandler`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ev-app\apps\web\src\contexts\AuthContext.tsx#withAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cobi_c%5CDesktop%5Cev-app%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();