'use client'

import { useAuth } from '@/contexts/AuthContext'
import { ProtectedRoute } from '@/components/ProtectedRoute'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Zap, Car, MapPin, BarChart3, Settings, LogOut } from 'lucide-react'

function DashboardContent() {
  const { user, profile, signOut } = useAuth()

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Zap className="h-8 w-8 text-electric-600 mr-2" />
              <span className="text-xl font-bold text-gray-900">GreenMilesEV</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {profile?.full_name || user?.email}
              </span>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={handleSignOut}>
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
          <p className="text-gray-600">
            Welcome back! Here's an overview of your electric vehicle activity.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Battery Level</CardTitle>
              <Zap className="h-4 w-4 text-electric-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-electric-600">85%</div>
              <p className="text-xs text-muted-foreground">247 miles range</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Car className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">847</div>
              <p className="text-xs text-muted-foreground">miles driven</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Charging Cost</CardTitle>
              <BarChart3 className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$42</div>
              <p className="text-xs text-muted-foreground">this month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Carbon Saved</CardTitle>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                127 lbs CO₂
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">$89</div>
              <p className="text-xs text-muted-foreground">money saved</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <Button className="h-20 flex-col bg-electric-600 hover:bg-electric-700">
                <MapPin className="h-6 w-6 mb-2" />
                Find Charging
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Car className="h-6 w-6 mb-2" />
                My Vehicle
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <BarChart3 className="h-6 w-6 mb-2" />
                Analytics
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <Settings className="h-6 w-6 mb-2" />
                Settings
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Your latest trips and charging sessions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="bg-electric-100 rounded-full p-2">
                    <Zap className="h-4 w-4 text-electric-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Charging Complete</p>
                    <p className="text-xs text-gray-500">Tesla Supercharger - Downtown</p>
                  </div>
                  <span className="text-xs text-gray-500">2h ago</span>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="bg-blue-100 rounded-full p-2">
                    <Car className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Trip Completed</p>
                    <p className="text-xs text-gray-500">Home to Office - 24.5 miles</p>
                  </div>
                  <span className="text-xs text-gray-500">5h ago</span>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="bg-green-100 rounded-full p-2">
                    <BarChart3 className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Weekly Report</p>
                    <p className="text-xs text-gray-500">4.2 mi/kWh efficiency</p>
                  </div>
                  <span className="text-xs text-gray-500">1d ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Vehicle Status */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Status</CardTitle>
            <CardDescription>Current status of your Tesla Model 3</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-electric-600 mb-2">85%</div>
                <p className="text-sm text-gray-600">Battery Level</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div className="bg-electric-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">247</div>
                <p className="text-sm text-gray-600">Miles Range</p>
                <p className="text-xs text-gray-500 mt-2">Estimated remaining</p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">Parked</div>
                <p className="text-sm text-gray-600">Status</p>
                <p className="text-xs text-gray-500 mt-2">Home garage</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}
