import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Link, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../src/contexts/AuthContext';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const { resetPassword } = useAuth();
  const router = useRouter();

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    setLoading(true);
    try {
      const { error } = await resetPassword(email);
      
      if (error) {
        Alert.alert('Reset Failed', error.message);
      } else {
        setSuccess(true);
      }
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1 bg-electric-50"
      >
        <View className="flex-1 justify-center px-6">
          <View className="bg-white rounded-2xl p-6 shadow-lg">
            <View className="items-center">
              <View className="bg-electric-100 rounded-full p-4 mb-4">
                <Ionicons name="mail" size={32} color="#22c55e" />
              </View>
              <Text className="text-2xl font-bold text-gray-900 mb-2 text-center">
                Check your email
              </Text>
              <Text className="text-gray-600 text-center mb-6">
                We've sent a password reset link to{'\n'}
                <Text className="font-semibold">{email}</Text>
              </Text>
              <Text className="text-sm text-gray-500 text-center mb-8">
                Didn't receive the email? Check your spam folder or try again.
              </Text>
              
              <View className="w-full space-y-3">
                <TouchableOpacity
                  onPress={() => setSuccess(false)}
                  className="bg-white border border-gray-200 rounded-lg py-4 items-center"
                >
                  <Text className="text-gray-700 font-semibold">Try again</Text>
                </TouchableOpacity>
                
                <Link href="/auth/signin" asChild>
                  <TouchableOpacity className="bg-electric-600 rounded-lg py-4 items-center">
                    <Text className="text-white font-semibold">Back to Sign In</Text>
                  </TouchableOpacity>
                </Link>
              </View>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-electric-50"
    >
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} className="px-6">
        {/* Header */}
        <View className="flex-1 justify-center py-12">
          <View className="items-center mb-8">
            <View className="flex-row items-center mb-4">
              <Ionicons name="flash" size={32} color="#22c55e" />
              <Text className="text-3xl font-bold text-gray-900 ml-2">GreenMilesEV</Text>
            </View>
            <Text className="text-2xl font-bold text-gray-900 mb-2">Reset your password</Text>
            <Text className="text-gray-600 text-center">
              Enter your email to receive a reset link
            </Text>
          </View>

          {/* Reset Password Form */}
          <View className="bg-white rounded-2xl p-6 shadow-lg">
            <Text className="text-xl font-bold text-gray-900 mb-6">Forgot Password</Text>
            
            {/* Email Input */}
            <View className="mb-6">
              <Text className="text-sm font-medium text-gray-700 mb-2">Email</Text>
              <View className="bg-gray-50 rounded-lg px-4 py-3 border border-gray-200">
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                  className="text-gray-900 text-base"
                />
              </View>
            </View>

            {/* Reset Button */}
            <TouchableOpacity
              onPress={handleResetPassword}
              disabled={loading || !email.trim()}
              className={`bg-electric-600 rounded-lg py-4 items-center ${
                loading || !email.trim() ? 'opacity-50' : ''
              }`}
            >
              {loading ? (
                <View className="flex-row items-center">
                  <ActivityIndicator color="white" size="small" />
                  <Text className="text-white font-semibold ml-2">Sending reset link...</Text>
                </View>
              ) : (
                <Text className="text-white font-semibold text-base">Send Reset Link</Text>
              )}
            </TouchableOpacity>

            {/* Back to Sign In */}
            <View className="mt-6 items-center">
              <Link href="/auth/signin" asChild>
                <TouchableOpacity 
                  disabled={loading}
                  className="flex-row items-center"
                >
                  <Ionicons name="arrow-back" size={16} color="#22c55e" />
                  <Text className="text-electric-600 font-medium ml-2">Back to Sign In</Text>
                </TouchableOpacity>
              </Link>
            </View>
          </View>

          {/* Additional Help */}
          <View className="mt-6 items-center">
            <Text className="text-gray-600 text-center">
              Remember your password?{' '}
            </Text>
            <Link href="/auth/signin" asChild>
              <TouchableOpacity disabled={loading}>
                <Text className="text-electric-600 font-medium">Sign in</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
