# @greenmiles-ev/shared

Shared types, utilities, and constants for the GreenMilesEV web and mobile applications.

## 📦 What's Included

### Types
- **Database Types**: Complete TypeScript interfaces matching the Supabase schema
- **API Types**: Request/response interfaces for API operations
- **Form Types**: Interfaces for form data validation
- **Analytics Types**: Interfaces for analytics and reporting data

### Utilities
- **Distance Calculations**: Haversine formula for geographic distance
- **EV Calculations**: Range, efficiency, charging time, and cost calculations
- **Formatting**: Currency, duration, distance, and date formatting
- **Validation**: Email, VIN, and other data validation functions

### Constants
- **Vehicle Data**: Makes, models, and specifications
- **Charging Networks**: Supported charging networks and connector types
- **Service Types**: Maintenance and service categories

## 🚀 Usage

### In Web App (Next.js)
```typescript
import { Vehicle, calculateEstimatedRange, VEHICLE_MAKES } from '@/shared/types'

// Use shared types
const vehicle: Vehicle = {
  id: 'uuid',
  make: 'Tesla',
  model: 'Model 3',
  // ... other properties
}

// Use utility functions
const estimatedRange = calculateEstimatedRange(85, 358) // 304 miles

// Use constants
const makeOptions = VEHICLE_MAKES.map(make => ({ label: make, value: make }))
```

### In Mobile App (React Native)
```typescript
import { ChargingStation, calculateDistance, CHARGING_NETWORKS } from '@/shared/types'

// Calculate distance to charging station
const distance = calculateDistance(
  userLatitude,
  userLongitude,
  station.latitude,
  station.longitude
)

// Filter by supported networks
const supportedNetworks = CHARGING_NETWORKS.filter(network => 
  userPreferences.networks.includes(network)
)
```

## 📋 Available Types

### Core Data Types
```typescript
// User and profile data
interface User
interface Profile

// Vehicle management
interface Vehicle
interface VehicleFormData

// Charging infrastructure
interface ChargingStation
interface ChargingSession

// Trip tracking
interface Trip

// Maintenance tracking
interface MaintenanceRecord

// Analytics and reporting
interface VehicleAnalytics
interface ChargingAnalytics
```

### Database Types
```typescript
// Complete Supabase database schema
interface Database {
  public: {
    Tables: {
      profiles: { Row, Insert, Update }
      vehicles: { Row, Insert, Update }
      charging_stations: { Row, Insert, Update }
      charging_sessions: { Row, Insert, Update }
      trips: { Row, Insert, Update }
      maintenance_records: { Row, Insert, Update }
    }
  }
}
```

## 🧮 Utility Functions

### Distance and Location
```typescript
// Calculate distance between two GPS coordinates
calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number

// Sort charging stations by distance from user
sortStationsByDistance(stations: ChargingStation[], userLat: number, userLon: number): ChargingStation[]
```

### EV Calculations
```typescript
// Calculate estimated range based on battery level
calculateEstimatedRange(batteryLevel: number, maxRange: number): number

// Calculate charging time estimate
calculateChargingTime(
  currentLevel: number, 
  targetLevel: number, 
  batteryCapacity: number, 
  chargingPower: number
): number

// Calculate trip efficiency
calculateEfficiency(distanceMiles: number, energyUsedKwh: number): number

// Calculate charging cost
calculateChargingCost(energyAdded: number, pricePerKwh: number): number
```

### Environmental Impact
```typescript
// Calculate carbon savings vs gas vehicle
calculateCarbonSavings(distanceMiles: number, gasVehicleMpg?: number): number

// Calculate money savings vs gas vehicle
calculateMoneySavings(
  distanceMiles: number,
  electricityCost: number,
  energyUsedKwh: number,
  gasPrice?: number,
  gasVehicleMpg?: number
): number
```

### Formatting
```typescript
// Format duration in minutes to human readable
formatDuration(minutes: number): string // "2h 30m"

// Format distance with appropriate units
formatDistance(miles: number): string // "1.5 mi" or "2640 ft"

// Format currency
formatCurrency(amount: number): string // "$12.50"

// Format relative time
formatRelativeTime(date: string | Date): string // "2h ago"
```

### Validation
```typescript
// Validate email format
isValidEmail(email: string): boolean

// Validate VIN format
isValidVIN(vin: string): boolean
```

### UI Helpers
```typescript
// Get battery level color based on percentage
getBatteryLevelColor(level: number): string

// Get charging station availability status
getAvailabilityStatus(available: number, total: number): 'available' | 'busy' | 'full'

// Generate random vehicle color
getRandomVehicleColor(): string

// Debounce function for search inputs
debounce<T>(func: T, wait: number): T
```

## 📊 Constants

### Vehicle Data
```typescript
const VEHICLE_MAKES = [
  'Tesla', 'BMW', 'Audi', 'Mercedes-Benz', 'Volkswagen',
  'Nissan', 'Chevrolet', 'Ford', 'Hyundai', 'Kia',
  'Volvo', 'Polestar', 'Lucid', 'Rivian', 'Other'
] as const
```

### Charging Infrastructure
```typescript
const CHARGING_NETWORKS = [
  'Tesla Supercharger', 'ChargePoint', 'Electrify America',
  'EVgo', 'Blink', 'Volta', 'Greenlots', 'Other'
] as const

const CONNECTOR_TYPES = [
  'Tesla', 'CCS', 'CHAdeMO', 'Type 2', 'J1772'
] as const
```

### Trip and Service Types
```typescript
const TRIP_TYPES = [
  'commute', 'leisure', 'business', 'other'
] as const

const SERVICE_TYPES = [
  'tire_rotation', 'brake_inspection', 'software_update',
  'battery_check', 'general_inspection', 'recall_service',
  'warranty_repair', 'other'
] as const
```

## 🔧 Development

### Building the Package
```bash
# Build TypeScript to JavaScript
npm run build

# Watch for changes during development
npm run dev

# Type check without building
npm run type-check
```

### Using in Development
The package is automatically linked in the monorepo workspace. Changes are immediately available in both web and mobile apps.

### Path Aliases
Both apps are configured with path aliases to import from the shared package:

```typescript
// Web app (Next.js)
import { Vehicle } from '@/shared/types'

// Mobile app (React Native)
import { Vehicle } from '@/shared/types'
```

## 📝 Contributing

When adding new types or utilities:

1. **Types**: Add to appropriate file in `src/types/`
2. **Utilities**: Add to `src/utils/index.ts`
3. **Constants**: Add to `src/types/index.ts`
4. **Export**: Make sure to export from `src/index.ts`
5. **Documentation**: Update this README with usage examples

### Type Safety
All functions should be fully typed with TypeScript. Use strict mode and avoid `any` types.

### Testing
Utility functions should include JSDoc comments with examples:

```typescript
/**
 * Calculate the distance between two points using the Haversine formula
 * @param lat1 - Latitude of first point
 * @param lon1 - Longitude of first point
 * @param lat2 - Latitude of second point
 * @param lon2 - Longitude of second point
 * @returns Distance in miles
 * @example
 * const distance = calculateDistance(34.0522, -118.2437, 34.0622, -118.2537)
 * console.log(distance) // 0.69 miles
 */
```

## 🔗 Related Packages

- **Web App**: `@greenmiles-ev/web` - Next.js web application
- **Mobile App**: `@greenmiles-ev/mobile` - React Native mobile application

## 📄 License

MIT License - see the root LICENSE file for details.
