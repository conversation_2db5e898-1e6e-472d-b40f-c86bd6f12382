#!/usr/bin/env node

/**
 * Environment Setup Script for GreenMilesEV
 * 
 * This script helps set up environment variables for both web and mobile apps.
 * Run with: node scripts/setup-env.js
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function setupEnvironment() {
  console.log('🚗 GreenMilesEV Environment Setup')
  console.log('=====================================\n')
  
  console.log('This script will help you set up environment variables for both web and mobile apps.\n')
  
  // Get Supabase credentials
  console.log('📡 Supabase Configuration')
  console.log('You can find these values in your Supabase dashboard at:')
  console.log('https://supabase.com/dashboard/project/pbevpexclffmhqstwlha/settings/api\n')
  
  const supabaseUrl = await question('Enter your Supabase URL (https://pbevpexclffmhqstwlha.supabase.co): ')
  const supabaseAnonKey = await question('Enter your Supabase Anon Key: ')
  const supabaseServiceKey = await question('Enter your Supabase Service Role Key (optional, press Enter to skip): ')
  
  // Optional services
  console.log('\n🗺️  Optional Services (press Enter to skip)')
  const googleMapsKey = await question('Google Maps API Key (for enhanced location features): ')
  const mapboxToken = await question('Mapbox Access Token (alternative to Google Maps): ')
  const googleAnalyticsId = await question('Google Analytics ID (for web analytics): ')
  
  // Create web app .env.local
  const webEnvPath = path.join(__dirname, '..', 'apps', 'web', '.env.local')
  const webEnvContent = `# GreenMilesEV Web App Environment Variables
# Generated on ${new Date().toISOString()}

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl || 'https://pbevpexclffmhqstwlha.supabase.co'}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${supabaseAnonKey}
${supabaseServiceKey ? `SUPABASE_SERVICE_ROLE_KEY=${supabaseServiceKey}` : '# SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here'}

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional Services
${googleAnalyticsId ? `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=${googleAnalyticsId}` : '# NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id_here'}
${googleMapsKey ? `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${googleMapsKey}` : '# NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here'}
${mapboxToken ? `NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=${mapboxToken}` : '# NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here'}
`

  // Create mobile app .env
  const mobileEnvPath = path.join(__dirname, '..', 'apps', 'mobile', '.env')
  const mobileEnvContent = `# GreenMilesEV Mobile App Environment Variables
# Generated on ${new Date().toISOString()}

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=${supabaseUrl || 'https://pbevpexclffmhqstwlha.supabase.co'}
EXPO_PUBLIC_SUPABASE_ANON_KEY=${supabaseAnonKey}

# App Configuration
EXPO_PUBLIC_APP_URL=http://localhost:3000

# Optional Services
${googleMapsKey ? `EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${googleMapsKey}` : '# EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here'}
${mapboxToken ? `EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN=${mapboxToken}` : '# EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here'}
`

  try {
    // Write web app environment file
    fs.writeFileSync(webEnvPath, webEnvContent)
    console.log(`\n✅ Created web app environment file: ${webEnvPath}`)
    
    // Write mobile app environment file
    fs.writeFileSync(mobileEnvPath, mobileEnvContent)
    console.log(`✅ Created mobile app environment file: ${mobileEnvPath}`)
    
    console.log('\n🎉 Environment setup complete!')
    console.log('\nNext steps:')
    console.log('1. Start the web app: cd apps/web && npm run dev')
    console.log('2. Start the mobile app: cd apps/mobile && npm start')
    console.log('3. Test authentication and data operations')
    
    console.log('\n📚 Documentation:')
    console.log('- Web app: apps/web/README.md')
    console.log('- Mobile app: apps/mobile/README.md')
    console.log('- Supabase setup: docs/SUPABASE_SETUP.md')
    
  } catch (error) {
    console.error('\n❌ Error creating environment files:', error.message)
    process.exit(1)
  }
  
  rl.close()
}

// Validate required directories exist
const webDir = path.join(__dirname, '..', 'apps', 'web')
const mobileDir = path.join(__dirname, '..', 'apps', 'mobile')

if (!fs.existsSync(webDir)) {
  console.error('❌ Web app directory not found. Make sure you\'re running this from the project root.')
  process.exit(1)
}

if (!fs.existsSync(mobileDir)) {
  console.error('❌ Mobile app directory not found. Make sure you\'re running this from the project root.')
  process.exit(1)
}

// Run the setup
setupEnvironment().catch(error => {
  console.error('❌ Setup failed:', error.message)
  process.exit(1)
})
