'use client'

import { useState } from 'react'
import { ProtectedRoute } from '@/components/ProtectedRoute'
import { Header } from '@/components/Header'
import { ThemeToggle } from '@/components/ThemeToggle'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  Bell, 
  Shield, 
  Globe, 
  Palette, 
  Zap,
  Save,
  Eye,
  EyeOff,
  Smartphone,
  Mail,
  Lock
} from 'lucide-react'

function SettingsContent() {
  const [notifications, setNotifications] = useState({
    charging: true,
    trips: true,
    maintenance: false,
    marketing: false,
    security: true
  })

  const [privacy, setPrivacy] = useState({
    profileVisible: true,
    shareData: false,
    analytics: true
  })

  const [preferences, setPreferences] = useState({
    units: 'imperial',
    language: 'en',
    autoLock: true,
    biometric: false
  })

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [key]: value }))
  }

  const handlePrivacyChange = (key: string, value: boolean) => {
    setPrivacy(prev => ({ ...prev, [key]: value }))
  }

  const handlePreferenceChange = (key: string, value: string | boolean) => {
    setPreferences(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header variant="dashboard" />

      {/* Main Content */}
      <main className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage your app preferences and account settings
          </p>
        </div>

        <div className="space-y-6">
          {/* Appearance Settings */}
          <Card className="dark:bg-gray-800 dark:border-gray-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Palette className="h-5 w-5 text-electric-600" />
                <CardTitle className="dark:text-white">Appearance</CardTitle>
              </div>
              <CardDescription className="dark:text-gray-300">
                Customize how the app looks and feels
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Theme</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Choose between light and dark mode
                  </p>
                </div>
                <ThemeToggle />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Units</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Distance and energy units
                  </p>
                </div>
                <select
                  value={preferences.units}
                  onChange={(e) => handlePreferenceChange('units', e.target.value)}
                  className="rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white"
                >
                  <option value="imperial">Imperial (mi, °F)</option>
                  <option value="metric">Metric (km, °C)</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Language</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    App display language
                  </p>
                </div>
                <select
                  value={preferences.language}
                  onChange={(e) => handlePreferenceChange('language', e.target.value)}
                  className="rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-white"
                >
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card className="dark:bg-gray-800 dark:border-gray-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Bell className="h-5 w-5 text-electric-600" />
                <CardTitle className="dark:text-white">Notifications</CardTitle>
              </div>
              <CardDescription className="dark:text-gray-300">
                Choose what notifications you want to receive
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { key: 'charging', label: 'Charging Updates', desc: 'Session start, completion, and errors' },
                { key: 'trips', label: 'Trip Notifications', desc: 'Trip summaries and efficiency reports' },
                { key: 'maintenance', label: 'Maintenance Reminders', desc: 'Service due dates and recommendations' },
                { key: 'security', label: 'Security Alerts', desc: 'Login attempts and account changes' },
                { key: 'marketing', label: 'Marketing Communications', desc: 'Product updates and promotions' }
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium dark:text-gray-300">{item.label}</Label>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{item.desc}</p>
                  </div>
                  <Button
                    variant={notifications[item.key as keyof typeof notifications] ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationChange(item.key, !notifications[item.key as keyof typeof notifications])}
                    className={notifications[item.key as keyof typeof notifications] 
                      ? "bg-electric-600 hover:bg-electric-700" 
                      : "dark:border-gray-600 dark:text-gray-300"
                    }
                  >
                    {notifications[item.key as keyof typeof notifications] ? 'On' : 'Off'}
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Privacy & Security */}
          <Card className="dark:bg-gray-800 dark:border-gray-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-electric-600" />
                <CardTitle className="dark:text-white">Privacy & Security</CardTitle>
              </div>
              <CardDescription className="dark:text-gray-300">
                Control your privacy and security settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Profile Visibility</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Make your profile visible to other users
                  </p>
                </div>
                <Button
                  variant={privacy.profileVisible ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePrivacyChange('profileVisible', !privacy.profileVisible)}
                  className={privacy.profileVisible 
                    ? "bg-electric-600 hover:bg-electric-700" 
                    : "dark:border-gray-600 dark:text-gray-300"
                  }
                >
                  {privacy.profileVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Data Sharing</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Share anonymized data to improve the service
                  </p>
                </div>
                <Button
                  variant={privacy.shareData ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePrivacyChange('shareData', !privacy.shareData)}
                  className={privacy.shareData 
                    ? "bg-electric-600 hover:bg-electric-700" 
                    : "dark:border-gray-600 dark:text-gray-300"
                  }
                >
                  {privacy.shareData ? 'On' : 'Off'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Auto-Lock</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Automatically lock the app when inactive
                  </p>
                </div>
                <Button
                  variant={preferences.autoLock ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePreferenceChange('autoLock', !preferences.autoLock)}
                  className={preferences.autoLock 
                    ? "bg-electric-600 hover:bg-electric-700" 
                    : "dark:border-gray-600 dark:text-gray-300"
                  }
                >
                  <Lock className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium dark:text-gray-300">Biometric Authentication</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Use fingerprint or face recognition
                  </p>
                </div>
                <Button
                  variant={preferences.biometric ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePreferenceChange('biometric', !preferences.biometric)}
                  className={preferences.biometric 
                    ? "bg-electric-600 hover:bg-electric-700" 
                    : "dark:border-gray-600 dark:text-gray-300"
                  }
                >
                  {preferences.biometric ? 'On' : 'Off'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Account Actions */}
          <Card className="dark:bg-gray-800 dark:border-gray-700">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-electric-600" />
                <CardTitle className="dark:text-white">Account Actions</CardTitle>
              </div>
              <CardDescription className="dark:text-gray-300">
                Manage your account and data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full justify-start dark:border-gray-600 dark:text-gray-300">
                <Mail className="mr-2 h-4 w-4" />
                Change Email Address
              </Button>
              <Button variant="outline" className="w-full justify-start dark:border-gray-600 dark:text-gray-300">
                <Lock className="mr-2 h-4 w-4" />
                Change Password
              </Button>
              <Button variant="outline" className="w-full justify-start dark:border-gray-600 dark:text-gray-300">
                <Smartphone className="mr-2 h-4 w-4" />
                Two-Factor Authentication
              </Button>
              <Button variant="destructive" className="w-full justify-start">
                Delete Account
              </Button>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button className="bg-electric-600 hover:bg-electric-700">
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default function SettingsPage() {
  return (
    <ProtectedRoute>
      <SettingsContent />
    </ProtectedRoute>
  )
}
