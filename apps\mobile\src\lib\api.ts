import { supabase } from './supabase'
import type { Vehicle, ChargingStation, ChargingSession, Trip, MaintenanceRecord } from '@/shared/types'

// Profile API
export const profileApi = {
  async getProfile() {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .single()
    return { data, error }
  },

  async updateProfile(updates: any) {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', (await supabase.auth.getUser()).data.user?.id)
      .select()
      .single()
    return { data, error }
  },
}

// Vehicle API
export const vehicleApi = {
  async getVehicles() {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .order('created_at', { ascending: false })
    return { data, error }
  },

  async getVehicle(id: string) {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .eq('id', id)
      .single()
    return { data, error }
  },

  async createVehicle(vehicle: Omit<Vehicle, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('vehicles')
      .insert([vehicle])
      .select()
      .single()
    return { data, error }
  },

  async updateVehicle(id: string, updates: Partial<Vehicle>) {
    const { data, error } = await supabase
      .from('vehicles')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    return { data, error }
  },

  async deleteVehicle(id: string) {
    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id)
    return { error }
  },
}

// Charging Station API
export const chargingStationApi = {
  async getChargingStations(limit = 50) {
    const { data, error } = await supabase
      .from('charging_stations')
      .select('*')
      .eq('status', 'operational')
      .limit(limit)
    return { data, error }
  },

  async getNearbyStations(latitude: number, longitude: number, radiusMiles = 25) {
    const { data, error } = await supabase
      .from('charging_stations')
      .select('*')
      .eq('status', 'operational')
    
    if (data && !error) {
      // Simple distance calculation
      const stationsWithDistance = data.map(station => ({
        ...station,
        distance: calculateDistance(latitude, longitude, station.latitude, station.longitude)
      })).filter(station => station.distance <= radiusMiles)
        .sort((a, b) => a.distance - b.distance)
      
      return { data: stationsWithDistance, error }
    }
    
    return { data, error }
  },
}

// Charging Session API
export const chargingSessionApi = {
  async getChargingSessions(limit = 50) {
    const { data, error } = await supabase
      .from('charging_sessions')
      .select(`
        *,
        vehicle:vehicles(make, model, year),
        charging_station:charging_stations(name, network, address)
      `)
      .order('start_time', { ascending: false })
      .limit(limit)
    return { data, error }
  },

  async createChargingSession(session: Omit<ChargingSession, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('charging_sessions')
      .insert([session])
      .select()
      .single()
    return { data, error }
  },

  async updateChargingSession(id: string, updates: Partial<ChargingSession>) {
    const { data, error } = await supabase
      .from('charging_sessions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    return { data, error }
  },
}

// Trip API
export const tripApi = {
  async getTrips(limit = 50) {
    const { data, error } = await supabase
      .from('trips')
      .select(`
        *,
        vehicle:vehicles(make, model, year)
      `)
      .order('start_time', { ascending: false })
      .limit(limit)
    return { data, error }
  },

  async createTrip(trip: Omit<Trip, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('trips')
      .insert([trip])
      .select()
      .single()
    return { data, error }
  },

  async updateTrip(id: string, updates: Partial<Trip>) {
    const { data, error } = await supabase
      .from('trips')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    return { data, error }
  },
}

// Maintenance API
export const maintenanceApi = {
  async getMaintenanceRecords(vehicleId?: string) {
    let query = supabase
      .from('maintenance_records')
      .select(`
        *,
        vehicle:vehicles(make, model, year)
      `)
      .order('service_date', { ascending: false })

    if (vehicleId) {
      query = query.eq('vehicle_id', vehicleId)
    }

    const { data, error } = await query
    return { data, error }
  },

  async createMaintenanceRecord(record: Omit<MaintenanceRecord, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('maintenance_records')
      .insert([record])
      .select()
      .single()
    return { data, error }
  },

  async updateMaintenanceRecord(id: string, updates: Partial<MaintenanceRecord>) {
    const { data, error } = await supabase
      .from('maintenance_records')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    return { data, error }
  },
}

// Analytics API
export const analyticsApi = {
  async getVehicleAnalytics(vehicleId: string, startDate?: string, endDate?: string) {
    // Get trips for the vehicle
    let tripsQuery = supabase
      .from('trips')
      .select('*')
      .eq('vehicle_id', vehicleId)
      .eq('status', 'completed')

    if (startDate) tripsQuery = tripsQuery.gte('start_time', startDate)
    if (endDate) tripsQuery = tripsQuery.lte('start_time', endDate)

    const { data: trips, error: tripsError } = await tripsQuery

    // Get charging sessions for the vehicle
    let sessionsQuery = supabase
      .from('charging_sessions')
      .select('*')
      .eq('vehicle_id', vehicleId)
      .eq('status', 'completed')

    if (startDate) sessionsQuery = sessionsQuery.gte('start_time', startDate)
    if (endDate) sessionsQuery = sessionsQuery.lte('start_time', endDate)

    const { data: sessions, error: sessionsError } = await sessionsQuery

    if (tripsError || sessionsError) {
      return { data: null, error: tripsError || sessionsError }
    }

    // Calculate analytics
    const totalMiles = trips?.reduce((sum, trip) => sum + (trip.distance_miles || 0), 0) || 0
    const totalTrips = trips?.length || 0
    const totalEnergyUsed = trips?.reduce((sum, trip) => sum + (trip.energy_used_kwh || 0), 0) || 0
    const totalChargingCost = sessions?.reduce((sum, session) => sum + (session.cost_total || 0), 0) || 0
    const averageEfficiency = totalEnergyUsed > 0 ? totalMiles / totalEnergyUsed : 0

    // Estimate carbon and money savings
    const carbonSavedLbs = totalMiles * 0.89
    const moneySaved = (totalMiles / 25) * 3.50 - totalChargingCost

    const analytics = {
      total_miles: Math.round(totalMiles * 10) / 10,
      total_trips: totalTrips,
      average_efficiency: Math.round(averageEfficiency * 100) / 100,
      total_energy_used: Math.round(totalEnergyUsed * 10) / 10,
      total_charging_cost: Math.round(totalChargingCost * 100) / 100,
      carbon_saved_lbs: Math.round(carbonSavedLbs * 10) / 10,
      money_saved: Math.round(moneySaved * 100) / 100,
    }

    return { data: analytics, error: null }
  },
}

// Helper function for distance calculation
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3959 // Earth's radius in miles
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}
