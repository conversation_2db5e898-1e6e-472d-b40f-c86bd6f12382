# Deployment Guide

This guide covers deploying the GreenMilesEV applications to production environments.

## 🌐 Web App Deployment (Vercel)

### Prerequisites
- Vercel account
- GitHub repository
- Supabase project configured

### Steps

1. **Connect Repository to Vercel**
   ```bash
   # Install Vercel CLI (optional)
   npm install -g vercel
   
   # Deploy from project root
   cd apps/web
   vercel
   ```

2. **Configure Environment Variables**
   In Vercel dashboard, add these environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://pbevpexclffmhqstwlha.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NEXT_PUBLIC_APP_URL=https://your-app.vercel.app
   ```

3. **Update Supabase Auth Settings**
   - Go to Supabase Dashboard → Authentication → URL Configuration
   - Add your Vercel domain to allowed redirect URLs
   - Update site URL to your production domain

4. **Deploy**
   - Push to main branch for automatic deployment
   - Or use `vercel --prod` for manual deployment

### Alternative Platforms

#### Netlify
```bash
# Build command
npm run build

# Publish directory
out

# Environment variables (same as Vercel)
```

#### Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Deploy
railway login
railway link
railway up
```

## 📱 Mobile App Deployment

### Prerequisites
- Expo account
- EAS CLI installed: `npm install -g @expo/eas-cli`
- Apple Developer account (for iOS)
- Google Play Console account (for Android)

### Development Build

1. **Configure EAS**
   ```bash
   cd apps/mobile
   eas login
   eas build:configure
   ```

2. **Create Development Build**
   ```bash
   # iOS
   eas build --platform ios --profile development
   
   # Android
   eas build --platform android --profile development
   ```

### Production Build

1. **Update app.json**
   ```json
   {
     "expo": {
       "name": "GreenMilesEV",
       "slug": "greenmiles-ev",
       "version": "1.0.0",
       "ios": {
         "bundleIdentifier": "com.greenmiles.ev"
       },
       "android": {
         "package": "com.greenmiles.ev"
       }
     }
   }
   ```

2. **Build for Stores**
   ```bash
   # iOS App Store
   eas build --platform ios --profile production
   
   # Android Play Store
   eas build --platform android --profile production
   ```

3. **Submit to Stores**
   ```bash
   # iOS App Store
   eas submit --platform ios
   
   # Android Play Store
   eas submit --platform android
   ```

### Over-the-Air Updates

```bash
# Publish update
eas update --branch production --message "Bug fixes and improvements"
```

## 🗄️ Database Deployment

### Production Supabase Setup

1. **Upgrade to Pro Plan** (recommended for production)
   - Better performance
   - More storage
   - Advanced features

2. **Configure Production Settings**
   ```sql
   -- Enable additional extensions if needed
   CREATE EXTENSION IF NOT EXISTS postgis;
   CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
   ```

3. **Set Up Backups**
   - Enable automatic backups in Supabase dashboard
   - Configure backup retention policy
   - Test backup restoration process

4. **Monitor Performance**
   - Set up database monitoring
   - Configure alerts for high CPU/memory usage
   - Monitor query performance

### Environment-Specific Configuration

#### Staging Environment
```bash
# Create staging branch in Supabase
# Use separate environment variables
NEXT_PUBLIC_SUPABASE_URL=https://staging-project.supabase.co
EXPO_PUBLIC_SUPABASE_URL=https://staging-project.supabase.co
```

#### Production Environment
```bash
# Use production Supabase project
# Enable additional security features
# Configure custom domain (optional)
```

## 🔒 Security Considerations

### Environment Variables
- Never commit `.env` files to version control
- Use different keys for staging/production
- Rotate keys regularly
- Use service role keys only on server-side

### Database Security
- Review RLS policies before production
- Enable audit logging
- Set up monitoring for suspicious activity
- Regular security updates

### API Security
- Rate limiting (built into Supabase)
- Input validation on all endpoints
- CORS configuration
- SSL/TLS encryption (automatic with Supabase)

## 📊 Monitoring & Analytics

### Application Monitoring

#### Web App
```bash
# Add to web app
npm install @vercel/analytics

# In layout.tsx
import { Analytics } from '@vercel/analytics/react'
```

#### Mobile App
```bash
# Add Expo Analytics
expo install expo-analytics-amplitude
```

### Database Monitoring
- Use Supabase dashboard metrics
- Set up custom alerts
- Monitor query performance
- Track user growth

### Error Tracking
```bash
# Add Sentry for error tracking
npm install @sentry/nextjs @sentry/react-native
```

## 🚀 CI/CD Pipeline

### GitHub Actions Example

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  deploy-web:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm install
      - run: cd apps/web && npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}

  deploy-mobile:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm install -g @expo/eas-cli
      - run: cd apps/mobile && eas update --branch production
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
```

## 🧪 Testing in Production

### Smoke Tests
```bash
# Test critical user flows
1. User registration/login
2. Vehicle management
3. Charging station discovery
4. Trip tracking
5. Data synchronization
```

### Performance Testing
- Load testing with realistic user scenarios
- Database performance under load
- Mobile app performance on various devices
- Network connectivity edge cases

## 📈 Scaling Considerations

### Database Scaling
- Monitor connection pool usage
- Consider read replicas for heavy read workloads
- Optimize queries with proper indexing
- Use connection pooling (PgBouncer)

### Application Scaling
- Vercel automatically scales web app
- Mobile app scales with user downloads
- Consider CDN for static assets
- Implement caching strategies

### Cost Optimization
- Monitor Supabase usage and costs
- Optimize database queries
- Use appropriate Vercel plan
- Consider data archiving strategies

## 🆘 Troubleshooting

### Common Issues

1. **Environment Variables Not Loading**
   - Check variable names (NEXT_PUBLIC_ vs EXPO_PUBLIC_)
   - Verify deployment platform configuration
   - Restart development servers

2. **Database Connection Issues**
   - Verify Supabase project is active
   - Check API keys are correct
   - Review RLS policies

3. **Mobile App Build Failures**
   - Check Expo CLI version
   - Verify app.json configuration
   - Review native dependencies

4. **Authentication Issues**
   - Check redirect URLs in Supabase
   - Verify JWT configuration
   - Review auth flow implementation

### Support Resources
- [Supabase Documentation](https://supabase.com/docs)
- [Vercel Documentation](https://vercel.com/docs)
- [Expo Documentation](https://docs.expo.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
