# Header Component

A reusable header component for the GreenMilesEV application that provides consistent navigation across all pages.

## Features

- **Responsive Design**: Works on desktop and mobile devices
- **Authentication Aware**: Shows different content based on user authentication status
- **Two Variants**: Default for public pages, dashboard for authenticated pages
- **Mobile Menu**: Collapsible navigation for mobile devices
- **Active Link Highlighting**: Shows current page in navigation
- **Consistent Branding**: GreenMilesEV logo and styling

## Usage

### Basic Usage (Public Pages)

```tsx
import { Header } from '@/components/Header'

export default function PublicPage() {
  return (
    <div>
      <Header />
      {/* Your page content */}
    </div>
  )
}
```

### Dashboard Variant (Authenticated Pages)

```tsx
import { Header } from '@/components/Header'

export default function DashboardPage() {
  return (
    <div>
      <Header variant="dashboard" />
      {/* Your page content */}
    </div>
  )
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'dashboard'` | `'default'` | Header variant to use |

## Variants

### Default Variant
- Used for public pages (home, landing pages)
- Shows public navigation links (Home, Features, About, Contact)
- Shows Sign In / Get Started buttons for unauthenticated users
- Shows Dashboard / Sign Out buttons for authenticated users

### Dashboard Variant
- Used for authenticated pages (dashboard, vehicles, charging, etc.)
- Shows authenticated navigation links (Dashboard, My Vehicles, Charging, Analytics)
- Shows user welcome message and profile actions
- Includes settings and sign out buttons

## Navigation Links

### Public Navigation (Default Variant)
- Home (/)
- Features (/#features)
- About (/#about)
- Contact (/#contact)

### Authenticated Navigation (Dashboard Variant)
- Dashboard (/dashboard)
- My Vehicles (/vehicles)
- Charging (/charging)
- Analytics (/analytics)

## Mobile Responsiveness

The header automatically adapts to mobile devices:
- Navigation links are hidden on mobile and replaced with a hamburger menu
- Mobile menu slides down when activated
- Clicking a link automatically closes the mobile menu
- Touch-friendly button sizes and spacing

## Authentication Integration

The header integrates with the `useAuth` hook to:
- Show/hide appropriate navigation items
- Display user information
- Handle sign out functionality
- Redirect to appropriate pages after authentication

## Styling

The header uses Tailwind CSS classes and follows the application's design system:
- Electric blue color scheme (`electric-600`, `electric-700`)
- Consistent spacing and typography
- Backdrop blur effect for modern appearance
- Smooth transitions and hover effects

## Examples

### Adding a New Page with Header

1. Create your page component:

```tsx
'use client'

import { Header } from '@/components/Header'
import { ProtectedRoute } from '@/components/ProtectedRoute'

function MyPageContent() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header variant="dashboard" />
      <main className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Your page content */}
      </main>
    </div>
  )
}

export default function MyPage() {
  return (
    <ProtectedRoute>
      <MyPageContent />
    </ProtectedRoute>
  )
}
```

2. Add the route to the navigation links in `Header.tsx` if needed.

### Customizing Navigation Links

To add or modify navigation links, edit the `authenticatedNavLinks` or `publicNavLinks` arrays in the Header component:

```tsx
const authenticatedNavLinks = [
  { href: '/dashboard', label: 'Dashboard' },
  { href: '/vehicles', label: 'My Vehicles' },
  { href: '/charging', label: 'Charging' },
  { href: '/analytics', label: 'Analytics' },
  { href: '/new-page', label: 'New Page' }, // Add new link here
]
```

## Footer Component

A companion `Footer` component is also available for consistent page footers:

```tsx
import { Footer } from '@/components/Footer'

export default function MyPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        {/* Your content */}
      </main>
      <Footer />
    </div>
  )
}
```
